@import url("variables.css");
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap");

/* Global Styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-family);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%);
  color: var(--text-primary);
  line-height: var(--line-height-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* Enhanced scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: var(--border-radius-md);
}

::-webkit-scrollbar-thumb {
  background: var(--gradient-primary);
  border-radius: var(--border-radius-md);
  transition: all var(--transition-medium);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gradient-accent);
}

/* Base Layout with Modern Design */
.settings-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--spacing-xl);
  font-family: var(--font-family);
  color: var(--text-primary);
  min-height: 100vh;
  line-height: var(--line-height-normal);
  position: relative;
}

/* Animated Background Pattern */
.settings-container::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
      circle at 20% 80%,
      rgba(99, 102, 241, 0.05) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(139, 92, 246, 0.05) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 40%,
      rgba(6, 182, 212, 0.03) 0%,
      transparent 50%
    );
  pointer-events: none;
  z-index: -1;
}

/* Enhanced Header with Advanced Effects and Modern Design */
.settings-header {
  position: relative;
  margin-bottom: var(--spacing-2xl);
  border-radius: var(--border-radius-2xl);
  overflow: hidden;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.98) 0%,
    rgba(248, 250, 252, 0.95) 50%,
    rgba(241, 245, 249, 0.98) 100%
  );
  box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.1),
    0 16px 32px -8px rgba(0, 0, 0, 0.08), 0 8px 16px -4px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.9), 0 0 0 1px rgba(255, 255, 255, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(32px) saturate(180%);
  transition: all var(--transition-medium);
  animation: headerFloat 8s ease-in-out infinite;
  min-height: 120px;
}

@keyframes headerFloat {
  0%,
  100% {
    transform: translateY(0px) scale(1);
  }
  25% {
    transform: translateY(-2px) scale(1.002);
  }
  50% {
    transform: translateY(-1px) scale(1.001);
  }
  75% {
    transform: translateY(-3px) scale(1.003);
  }
}

.settings-header:hover {
  transform: translateY(-6px) scale(1.01);
  box-shadow: 0 48px 96px -12px rgba(0, 0, 0, 0.15),
    0 24px 48px -8px rgba(0, 0, 0, 0.12), 0 12px 24px -4px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 1), 0 0 0 1px rgba(255, 255, 255, 0.5);
}

/* Enhanced Background Effects */
.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: -2;
}

.header-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(
    90deg,
    var(--gradient-primary) 0%,
    var(--gradient-accent) 25%,
    var(--gradient-warm) 50%,
    var(--gradient-cool) 75%,
    var(--gradient-primary) 100%
  );
  background-size: 400% 100%;
  animation: gradientFlow 8s ease-in-out infinite;
}

@keyframes gradientFlow {
  0%,
  100% {
    background-position: 0% 50%;
  }
  25% {
    background-position: 25% 50%;
  }
  50% {
    background-position: 50% 50%;
  }
  75% {
    background-position: 75% 50%;
  }
}

/* Floating Particles */
.floating-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: -1;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: radial-gradient(
    circle,
    rgba(99, 102, 241, 0.6) 0%,
    transparent 70%
  );
  border-radius: 50%;
  animation: particleFloat 12s infinite linear;
}

.particle:nth-child(1) {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
  animation-duration: 15s;
}

.particle:nth-child(2) {
  top: 60%;
  left: 20%;
  animation-delay: 3s;
  animation-duration: 18s;
}

.particle:nth-child(3) {
  top: 40%;
  right: 15%;
  animation-delay: 6s;
  animation-duration: 20s;
}

.particle:nth-child(4) {
  top: 80%;
  right: 30%;
  animation-delay: 9s;
  animation-duration: 16s;
}

.particle:nth-child(5) {
  top: 30%;
  left: 50%;
  animation-delay: 12s;
  animation-duration: 14s;
}

@keyframes particleFloat {
  0% {
    transform: translateY(0px) translateX(0px) scale(1);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  50% {
    transform: translateY(-20px) translateX(10px) scale(1.2);
    opacity: 0.8;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-40px) translateX(-5px) scale(0.8);
    opacity: 0;
  }
}

/* Header Content Layout */
.header-content {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xl) var(--spacing-2xl);
  min-height: 120px;
}

/* Title Section */
.title-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-2xl);
}

.title-icon {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  background: linear-gradient(
    135deg,
    var(--gradient-primary) 0%,
    var(--gradient-accent) 100%
  );
  border-radius: var(--border-radius-xl);
  box-shadow: 0 8px 32px rgba(99, 102, 241, 0.3),
    0 4px 16px rgba(99, 102, 241, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3);
  animation: iconPulse 4s ease-in-out infinite;
  transition: all var(--transition-medium);
}

.title-icon:hover {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 12px 48px rgba(99, 102, 241, 0.4),
    0 6px 24px rgba(99, 102, 241, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

@keyframes iconPulse {
  0%,
  100% {
    box-shadow: 0 8px 32px rgba(99, 102, 241, 0.3),
      0 4px 16px rgba(99, 102, 241, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }
  50% {
    box-shadow: 0 12px 48px rgba(99, 102, 241, 0.5),
      0 6px 24px rgba(99, 102, 241, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.4);
  }
}

.title-icon svg {
  color: white;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  transition: transform var(--transition-medium);
}

.title-icon:hover svg {
  transform: scale(1.1);
}

.title-text h1 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--text-primary) 100%
  );
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  letter-spacing: -0.025em;
  position: relative;
}

@keyframes titleShimmer {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.header-subtitle {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  opacity: 0.8;
  animation: subtitleFade 3s ease-in-out infinite;
}

@keyframes subtitleFade {
  0%,
  100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

/* Enhanced Header Actions */
.header-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-md);
}

.action-group {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

.header-actions .btn {
  position: relative;
  overflow: hidden;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--border-radius-xl);
  font-weight: var(--font-weight-semibold);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1), 0 4px 12px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all var(--transition-medium);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  min-height: 48px;
  backdrop-filter: blur(10px);
}

.btn-icon {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  transition: transform var(--transition-medium);
}

.btn-text {
  position: relative;
  z-index: 2;
  font-size: var(--font-size-sm);
  transition: transform var(--transition-medium);
}

.btn-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
  pointer-events: none;
}

.header-actions .btn:active .btn-ripple {
  width: 300px;
  height: 300px;
}

.header-actions .btn:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 16px 40px rgba(0, 0, 0, 0.15), 0 8px 20px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.header-actions .btn:hover .btn-icon {
  transform: scale(1.1) rotate(5deg);
}

.header-actions .btn:hover .btn-text {
  transform: translateX(2px);
}

/* Status Indicator */
.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  background: linear-gradient(
    135deg,
    rgba(16, 185, 129, 0.1) 0%,
    rgba(5, 150, 105, 0.15) 100%
  );
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: var(--border-radius-full);
  backdrop-filter: blur(10px);
}

.status-dot {
  width: 8px;
  height: 8px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 50%;
  animation: statusPulse 2s ease-in-out infinite;
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
}

@keyframes statusPulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

.status-text {
  font-size: var(--font-size-xs);
  color: #059669;
  font-weight: var(--font-weight-medium);
}

/* Header Wave Effect */
.header-wave {
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  overflow: hidden;
  line-height: 0;
  transform: rotate(180deg);
}

.header-wave svg {
  position: relative;
  display: block;
  width: calc(100% + 1.3px);
  height: 40px;
}

.header-wave .shape-fill {
  fill: transparent;
  animation: waveFlow 8s ease-in-out infinite;
}

@keyframes waveFlow {
  0%,
  100% {
    fill: rgba(99, 102, 241, 0.1);
  }
  25% {
    fill: rgba(139, 92, 246, 0.1);
  }
  50% {
    fill: rgba(6, 182, 212, 0.1);
  }
  75% {
    fill: rgba(245, 158, 11, 0.1);
  }
}

/* ===== ADVANCED HEADER ENHANCEMENTS ===== */

/* Enhanced CSS Custom Properties */
:root {
  --header-primary-color: 99, 102, 241;
  --header-secondary-color: 139, 92, 246;
  --header-accent-color: 6, 182, 212;
  --header-warm-color: 245, 158, 11;
  --particle-glow: 0 0 20px rgba(var(--header-primary-color), 0.6);
  --glassmorphism-bg: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.9) 100%
  );
  --dynamic-blur: blur(20px) saturate(180%);
}

/* Universal Animation Base */
@keyframes universalFloat {
  0% {
    transform: var(--start-transform, translateY(0))
      scale(var(--start-scale, 1));
    opacity: var(--start-opacity, 0);
  }
  50% {
    transform: var(--mid-transform, translateY(-20px))
      scale(var(--mid-scale, 1.2));
    opacity: var(--mid-opacity, 0.8);
  }
  100% {
    transform: var(--end-transform, translateY(-40px))
      scale(var(--end-scale, 0.8));
    opacity: var(--end-opacity, 0);
  }
}

@keyframes universalRotate {
  0% {
    transform: rotate(0deg) scale(var(--rotate-scale, 1));
    opacity: var(--rotate-opacity, 0.3);
  }
  50% {
    opacity: 0.7;
  }
  100% {
    transform: rotate(360deg) scale(var(--rotate-scale-end, 1.2));
    opacity: var(--rotate-opacity, 0.3);
  }
}

@keyframes universalPulse {
  0%,
  100% {
    transform: var(--pulse-start, scale(1));
    opacity: var(--pulse-opacity-start, 0.5);
  }
  50% {
    transform: var(--pulse-mid, scale(1.2));
    opacity: var(--pulse-opacity-mid, 1);
  }
}

/* Consolidated Background Effects */
.header-background {
  position: absolute;
  inset: 0;
  overflow: hidden;
  z-index: -2;
}

.geometric-pattern {
  position: absolute;
  inset: 0;
  overflow: hidden;
  opacity: 0.03;
  z-index: 1;
  pointer-events: none;
}

.pattern-grid {
  position: relative;
  width: 100%;
  height: 100%;
}

/* Unified Pattern Elements */
.pattern-element {
  position: absolute;
  animation: universalFloat var(--duration, 20s) linear infinite;
  animation-delay: var(--delay, 0s);
}

.pattern-line {
  background: linear-gradient(
    45deg,
    transparent,
    rgba(var(--header-primary-color), 0.4),
    transparent
  );
  height: 1px;
  transform: rotate(var(--rotation, 0deg));
}

.pattern-line:nth-child(1) {
  width: 200px;
  top: 20%;
  left: -50px;
  --rotation: 15deg;
  --delay: 0s;
}
.pattern-line:nth-child(2) {
  width: 150px;
  top: 60%;
  right: -30px;
  --rotation: -25deg;
  --delay: 7s;
}
.pattern-line:nth-child(3) {
  width: 100px;
  top: 80%;
  left: 30%;
  --rotation: 45deg;
  --delay: 14s;
}

.pattern-circle {
  border: 1px solid rgba(var(--header-secondary-color), 0.3);
  border-radius: 50%;
  animation: universalRotate 25s linear infinite;
  animation-delay: var(--delay, 0s);
}

.pattern-circle:nth-child(4) {
  width: 80px;
  height: 80px;
  top: 30%;
  left: 70%;
  --delay: 0s;
}
.pattern-circle:nth-child(5) {
  width: 40px;
  height: 40px;
  top: 70%;
  left: 20%;
  --delay: 12s;
}

.pattern-triangle {
  width: 0;
  height: 0;
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
  border-bottom: 25px solid rgba(var(--header-accent-color), 0.2);
  animation: universalRotate 18s linear infinite;
  animation-delay: var(--delay, 0s);
}

.pattern-triangle:nth-child(6) {
  top: 15%;
  left: 40%;
  --delay: 0s;
}
.pattern-triangle:nth-child(7) {
  top: 75%;
  right: 25%;
  --delay: 9s;
  transform: rotate(180deg);
}

/* Consolidated Mesh & Particle System */
.gradient-mesh,
.particle-system,
.starfield {
  position: absolute;
  inset: 0;
  overflow: hidden;
  z-index: -1;
  pointer-events: none;
}

.particle-system {
  z-index: 2;
}

/* Universal Blob/Particle Base */
.dynamic-element {
  position: absolute;
  border-radius: 50%;
  animation: universalFloat var(--duration, 15s) ease-in-out infinite;
  animation-delay: var(--delay, 0s);
}

.mesh-blob {
  filter: blur(40px);
  opacity: 0.6;
  background: radial-gradient(
    circle,
    rgba(var(--color), var(--opacity, 0.4)) 0%,
    transparent 70%
  );
  animation-name: blobMorph;
}

.blob-1 {
  width: 300px;
  height: 300px;
  top: -150px;
  left: -100px;
  --color: var(--header-primary-color);
  --delay: 0s;
}
.blob-2 {
  width: 200px;
  height: 200px;
  top: 50%;
  right: -80px;
  --color: var(--header-secondary-color);
  --opacity: 0.3;
  --delay: 5s;
}
.blob-3 {
  width: 250px;
  height: 250px;
  bottom: -100px;
  left: 30%;
  --color: var(--header-accent-color);
  --opacity: 0.25;
  --delay: 10s;
}
.blob-4 {
  width: 180px;
  height: 180px;
  top: 20%;
  left: 60%;
  --color: var(--header-warm-color);
  --opacity: 0.2;
  --delay: 7s;
}

@keyframes blobMorph {
  0%,
  100% {
    transform: scale(1) translate(0, 0) rotate(0deg);
    border-radius: 50%;
  }
  25% {
    transform: scale(1.2) translate(20px, -15px) rotate(90deg);
    border-radius: 40% 60% 70% 30%;
  }
  50% {
    transform: scale(0.8) translate(-15px, 20px) rotate(180deg);
    border-radius: 70% 30% 40% 60%;
  }
  75% {
    transform: scale(1.1) translate(10px, 10px) rotate(270deg);
    border-radius: 60% 40% 30% 70%;
  }
}

/* Enhanced Effects with Shine */
.gradient-shine,
.wave-shimmer {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, var(--shine-opacity, 0.8)) 50%,
    transparent 100%
  );
  animation: shineEffect var(--shine-duration, 4s) ease-in-out infinite;
}

.gradient-shine {
  --shine-opacity: 1;
}

@keyframes shineEffect {
  0%,
  100% {
    transform: translateX(-100%);
    opacity: 0;
  }
  10%,
  90% {
    opacity: 1;
  }
  50% {
    transform: translateX(100%);
  }
}

/* Consolidated Particle & Star System */
.particle-layer {
  position: absolute;
  inset: 0;
}

.particle-base {
  position: absolute;
  border-radius: 50%;
  animation: universalFloat var(--duration, 20s) infinite linear;
  animation-delay: var(--delay, 0s);
}

.interactive-particle {
  width: var(--size, 6px);
  height: var(--size, 6px);
  background: radial-gradient(
    circle,
    rgba(var(--header-primary-color), 0.8) 0%,
    rgba(var(--header-secondary-color), 0.4) 50%,
    transparent 100%
  );
  box-shadow: var(--particle-glow);
  --duration: var(--particle-duration, 20s);
}

.micro-particle {
  width: var(--size, 3px);
  height: var(--size, 3px);
  background: radial-gradient(
    circle,
    rgba(var(--header-accent-color), 0.6) 0%,
    transparent 70%
  );
  --duration: 15s;
}

.star {
  width: var(--size, 2px);
  height: var(--size, 2px);
  background: white;
  box-shadow: 0 0 6px rgba(255, 255, 255, 0.8);
  animation: universalPulse 3s ease-in-out infinite;
  animation-delay: var(--delay, 0s);
  --pulse-start: scale(1);
  --pulse-mid: scale(1.5);
  --pulse-opacity-start: 0.3;
  --pulse-opacity-mid: 1;
}

/* Positioning Utilities */
.interactive-particle:nth-child(1) {
  top: 15%;
  left: 10%;
  --delay: 0s;
  --particle-duration: 18s;
}
.interactive-particle:nth-child(2) {
  top: 40%;
  left: 70%;
  --delay: 4s;
  --particle-duration: 22s;
}
.interactive-particle:nth-child(3) {
  top: 70%;
  left: 30%;
  --delay: 8s;
  --particle-duration: 16s;
}
.interactive-particle:nth-child(4) {
  top: 25%;
  left: 85%;
  --delay: 12s;
  --particle-duration: 20s;
}
.interactive-particle:nth-child(5) {
  top: 85%;
  left: 15%;
  --delay: 16s;
  --particle-duration: 24s;
}

.micro-particle:nth-child(1) {
  top: 20%;
  left: 20%;
  --delay: 0s;
}
.micro-particle:nth-child(2) {
  top: 50%;
  left: 60%;
  --delay: 2s;
}
.micro-particle:nth-child(3) {
  top: 80%;
  left: 40%;
  --delay: 4s;
}
.micro-particle:nth-child(4) {
  top: 35%;
  left: 80%;
  --delay: 6s;
}
.micro-particle:nth-child(5) {
  top: 65%;
  left: 10%;
  --delay: 8s;
}
.micro-particle:nth-child(6) {
  top: 10%;
  left: 50%;
  --delay: 10s;
}
.micro-particle:nth-child(7) {
  top: 90%;
  left: 70%;
  --delay: 12s;
}
.micro-particle:nth-child(8) {
  top: 45%;
  left: 25%;
  --delay: 14s;
}

.star:nth-child(1) {
  top: 10%;
  left: 15%;
  --delay: 0s;
}
.star:nth-child(2) {
  top: 25%;
  left: 75%;
  --delay: 0.5s;
}
.star:nth-child(3) {
  top: 45%;
  left: 35%;
  --delay: 1s;
}
.star:nth-child(4) {
  top: 65%;
  left: 85%;
  --delay: 1.5s;
}
.star:nth-child(5) {
  top: 80%;
  left: 25%;
  --delay: 2s;
}
.star:nth-child(6) {
  top: 15%;
  left: 55%;
  --delay: 2.5s;
}
.star:nth-child(7) {
  top: 35%;
  left: 10%;
  --delay: 0.3s;
}
.star:nth-child(8) {
  top: 55%;
  left: 65%;
  --delay: 0.8s;
}
.star:nth-child(9) {
  top: 75%;
  left: 45%;
  --delay: 1.3s;
}
.star:nth-child(10) {
  top: 90%;
  left: 80%;
  --delay: 1.8s;
}

/* Interactive Light Rays */
.light-rays {
  position: absolute;
  inset: 0;
  overflow: hidden;
  z-index: 1;
  pointer-events: none;
}

.light-ray {
  position: absolute;
  background: linear-gradient(
    45deg,
    transparent 0%,
    rgba(var(--header-primary-color), 0.1) 20%,
    rgba(var(--header-secondary-color), 0.2) 50%,
    rgba(var(--header-primary-color), 0.1) 80%,
    transparent 100%
  );
  transform-origin: center;
  animation: lightRayAnimation 8s ease-in-out infinite;
  animation-delay: var(--delay, 0s);
  transform: rotate(var(--rotation, 0deg));
  width: var(--width, 2px);
  height: var(--height, 200px);
}

.ray-1 {
  top: -50px;
  left: 20%;
  --rotation: 25deg;
  --delay: 0s;
  --width: 2px;
  --height: 200px;
}
.ray-2 {
  top: -30px;
  right: 30%;
  --rotation: -15deg;
  --delay: 2.5s;
  --width: 1px;
  --height: 150px;
}
.ray-3 {
  bottom: -60px;
  left: 60%;
  --rotation: 45deg;
  --delay: 5s;
  --width: 3px;
  --height: 180px;
}

@keyframes lightRayAnimation {
  0%,
  100% {
    opacity: 0;
    transform: scale(1) rotate(var(--rotation, 0deg));
  }
  50% {
    opacity: 0.8;
    transform: scale(1.2) rotate(calc(var(--rotation, 0deg) + 10deg));
  }
}

/* Enhanced Title Icon Container */
.title-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-backdrop {
  position: absolute;
  width: 100px;
  height: 100px;
  background: radial-gradient(
    circle,
    rgba(99, 102, 241, 0.1) 0%,
    transparent 70%
  );
  border-radius: 50%;
  animation: backdropPulse 4s ease-in-out infinite;
  z-index: -1;
}

@keyframes backdropPulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.3);
    opacity: 0.8;
  }
}

.icon-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 80px;
  height: 80px;
  background: radial-gradient(
    circle,
    rgba(99, 102, 241, 0.3) 0%,
    transparent 70%
  );
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: glowPulse 3s ease-in-out infinite;
  z-index: -1;
}

@keyframes glowPulse {
  0%,
  100% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.6;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 1;
  }
}

.icon-sparkles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.sparkle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: white;
  border-radius: 50%;
  box-shadow: 0 0 6px rgba(255, 255, 255, 0.8);
  animation: sparkleAnimation 2s ease-in-out infinite;
}

.sparkle-1 {
  top: 10px;
  right: 15px;
  animation-delay: 0s;
}

.sparkle-2 {
  bottom: 15px;
  left: 10px;
  animation-delay: 0.5s;
}

.sparkle-3 {
  top: 15px;
  left: 5px;
  animation-delay: 1s;
}

.sparkle-4 {
  bottom: 10px;
  right: 8px;
  animation-delay: 1.5s;
}

@keyframes sparkleAnimation {
  0%,
  100% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1) rotate(180deg);
  }
}

.icon-orbital-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 90px;
  height: 90px;
  border: 1px solid rgba(99, 102, 241, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: orbitalRotation 20s linear infinite;
  z-index: -1;
}

.icon-orbital-ring::before {
  content: "";
  position: absolute;
  top: -2px;
  left: 50%;
  width: 4px;
  height: 4px;
  background: rgba(99, 102, 241, 0.8);
  border-radius: 50%;
  transform: translateX(-50%);
  box-shadow: 0 0 8px rgba(99, 102, 241, 0.6);
}

@keyframes orbitalRotation {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* Animated Title */
.animated-title {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.title-word {
  display: inline-block;
  animation: wordAnimation 3s ease-in-out infinite;
  position: relative;
  color: var(--text-primary);
}

.title-word:nth-child(1) {
  animation-delay: 0s;
  color: var(--primary-color);
}

.title-word:nth-child(2) {
  animation-delay: 0.3s;
  color: var(--text-primary);
}

.title-word:nth-child(3) {
  animation-delay: 0.6s;
  color: var(--text-secondary);
}

@keyframes wordAnimation {
  0%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-5px);
  }
}

.subtitle-highlight {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-weight: var(--font-weight-semibold);
  animation: highlightGlow 2s ease-in-out infinite;
}

@keyframes highlightGlow {
  0%,
  100% {
    filter: drop-shadow(0 0 4px rgba(99, 102, 241, 0.3));
  }
  50% {
    filter: drop-shadow(0 0 8px rgba(99, 102, 241, 0.6));
  }
}

.subtitle-cursor {
  animation: cursorBlink 1s step-end infinite;
  color: var(--primary-color);
}

@keyframes cursorBlink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

/* Enhanced Interactive Elements */
.enhanced-btn,
.enhanced-status {
  position: relative;
  overflow: hidden;
  border: none;
  transition: all var(--transition-medium);
}

.btn-background,
.status-background {
  position: absolute;
  inset: 0;
  border-radius: inherit;
  overflow: hidden;
}

.btn-gradient {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.2) 0%,
    transparent 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
  opacity: 0;
  transition: opacity var(--transition-medium);
}

.enhanced-btn:hover .btn-gradient {
  opacity: 1;
}

.btn-shine {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.enhanced-btn:hover .btn-shine {
  transform: translateX(100%);
}

.btn-content,
.status-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.btn-particles {
  position: absolute;
  inset: 0;
  pointer-events: none;
  opacity: 0;
  transition: opacity var(--transition-medium);
}

.enhanced-btn:hover .btn-particles {
  opacity: 1;
}

.btn-particle {
  position: absolute;
  width: var(--size, 2px);
  height: var(--size, 2px);
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: universalFloat 1s ease-out infinite;
  animation-delay: var(--delay, 0s);
  --start-transform: scale(0) translateY(0);
  --mid-transform: scale(1) translateY(-10px);
  --end-transform: scale(0) translateY(-20px);
  --start-opacity: 0;
  --mid-opacity: 1;
  --end-opacity: 0;
}

.btn-particle:nth-child(1) {
  top: 20%;
  left: 30%;
  --delay: 0s;
}
.btn-particle:nth-child(2) {
  top: 60%;
  right: 25%;
  --delay: 0.3s;
}
.btn-particle:nth-child(3) {
  bottom: 30%;
  left: 60%;
  --delay: 0.6s;
}

/* Enhanced Status Indicator (Consolidated) */
.enhanced-status {
  border-radius: var(--border-radius-xl);
  backdrop-filter: var(--dynamic-blur);
}

.status-background {
  background: linear-gradient(
    135deg,
    rgba(16, 185, 129, 0.1) 0%,
    rgba(5, 150, 105, 0.15) 100%
  );
}

.status-content {
  padding: var(--spacing-sm) var(--spacing-md);
}

.status-dot {
  position: relative;
  width: 12px;
  height: 12px;
}

.dot-core,
.dot-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

.dot-core {
  width: 8px;
  height: 8px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.6);
}

.dot-pulse {
  width: 12px;
  height: 12px;
  border: 2px solid rgba(16, 185, 129, 0.4);
  animation: universalPulse 2s ease-in-out infinite;
  --pulse-start: translate(-50%, -50%) scale(1);
  --pulse-mid: translate(-50%, -50%) scale(2);
  --pulse-opacity-start: 1;
  --pulse-opacity-mid: 0;
}

.status-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: rgba(16, 185, 129, 0.2);
  border-radius: 0 0 var(--border-radius-xl) var(--border-radius-xl);
}

.progress-bar {
  position: absolute;
  inset: 0;
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
  border-radius: inherit;
  width: 0%;
  animation: progressAnimation 3s ease-in-out infinite;
}

@keyframes progressAnimation {
  0%,
  100% {
    width: 0%;
  }
  50% {
    width: 100%;
  }
}

/* Enhanced Wave Effects (Consolidated) */
.enhanced-wave {
  position: relative;
}

.wave-layer {
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  overflow: hidden;
  line-height: 0;
  transform: rotate(180deg);
}

.wave-primary {
  z-index: 2;
}
.wave-secondary {
  z-index: 1;
  opacity: 0.7;
}

.morphing-path {
  fill: rgba(var(--header-primary-color), var(--wave-opacity, 0.15));
  animation: morphingWave 12s ease-in-out infinite;
  animation-delay: var(--wave-delay, 0s);
}

.wave-secondary .morphing-path {
  --wave-opacity: 0.1;
  --wave-delay: 4s;
}

@keyframes morphingWave {
  0%,
  100% {
    fill: rgba(var(--header-primary-color), 0.15);
    transform: scaleY(1);
  }
  25% {
    fill: rgba(var(--header-secondary-color), 0.12);
    transform: scaleY(1.1);
  }
  50% {
    fill: rgba(var(--header-accent-color), 0.1);
    transform: scaleY(0.9);
  }
  75% {
    fill: rgba(var(--header-warm-color), 0.08);
    transform: scaleY(1.05);
  }
}

.wave-shimmer {
  --shine-duration: 6s;
}

/* Enhanced Navigation */
.settings-nav {
  position: sticky;
  top: var(--spacing-xl);
  float: left;
  width: 280px;
  margin-right: var(--spacing-xl);
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.98) 0%,
    rgba(248, 250, 252, 0.95) 100%
  );
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-lg);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1), 0 5px 15px rgba(0, 0, 0, 0.07),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(20px) saturate(180%);
  transition: all var(--transition-medium);
  overflow: hidden;
}

.settings-nav::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-accent);
  border-radius: var(--border-radius-xl) var(--border-radius-xl) 0 0;
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
  position: relative;
}

.nav-list li {
  margin-bottom: var(--spacing-xs);
}

.nav-link {
  display: flex;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  text-decoration: none;
  color: var(--text-secondary);
  border-radius: var(--border-radius-lg);
  transition: all var(--transition-medium);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  position: relative;
  overflow: hidden;
  border: 1px solid transparent;
}

.nav-link::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--gradient-primary);
  opacity: 0;
  transition: all var(--transition-medium);
  border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
}

.nav-link::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  opacity: 0;
  transition: opacity var(--transition-medium);
  border-radius: var(--border-radius-lg);
}

.nav-link:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
  transform: translateX(4px);
  border-color: var(--border-color);
  box-shadow: var(--shadow-md);
}

.nav-link:hover::before {
  opacity: 0.7;
}

.nav-link:hover::after {
  opacity: 0.05;
}

.nav-link.active {
  background: var(--bg-primary);
  color: var(--primary-color);
  font-weight: var(--font-weight-semibold);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-colored);
  transform: translateX(8px);
}

.nav-link.active::before {
  opacity: 1;
}

.nav-link.active::after {
  opacity: 0.1;
}

.nav-link svg {
  width: 20px;
  height: 20px;
  margin-right: var(--spacing-md);
  flex-shrink: 0;
  transition: transform var(--transition-medium);
  position: relative;
  z-index: 1;
}

.nav-link:hover svg {
  transform: scale(1.1);
}

.nav-link.active svg {
  transform: scale(1.15);
}

/* Enhanced Main Content */
.settings-content {
  margin-left: 320px;
  min-height: 600px;
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.98) 0%,
    rgba(248, 250, 252, 0.95) 100%
  );
  border-radius: var(--border-radius-xl);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15),
    0 10px 20px -5px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: var(--spacing-2xl);
  backdrop-filter: blur(20px) saturate(180%);
  position: relative;
  overflow: hidden;
}

.settings-content::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-cool);
  border-radius: var(--border-radius-xl) var(--border-radius-xl) 0 0;
}

.settings-section {
  display: none;
  opacity: 0;
  transform: translateY(10px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.settings-section.active {
  display: block;
  opacity: 1;
  transform: translateY(0);
}

.settings-section h2 {
  margin: 0 0 var(--spacing-2xl) 0;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  padding-bottom: var(--spacing-lg);
  border-bottom: 2px solid var(--border-color);
  position: relative;
  letter-spacing: -0.025em;
}

.settings-section h2::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 80px;
  height: 2px;
  background: var(--gradient-primary);
  border-radius: var(--border-radius-full);
}

.settings-section h2 svg {
  margin-right: var(--spacing-md);
  color: var(--primary-color);
}

/* Enhanced Setting Groups */
.setting-group {
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.8) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08), 0 4px 12px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.7);
  transition: all var(--transition-medium);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.setting-group::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-accent);
  opacity: 0;
  transition: opacity var(--transition-medium);
}

.setting-group:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-4px);
  border-color: var(--primary-color);
}

.setting-group:hover::before {
  opacity: 1;
}

.setting-group h3 {
  margin: 0 0 var(--spacing-lg) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  border-bottom: 2px solid var(--border-light);
  padding-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  position: relative;
}

.setting-group h3::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 40px;
  height: 2px;
  background: var(--gradient-accent);
  border-radius: var(--border-radius-full);
}

.setting-group h3 svg {
  margin-right: var(--spacing-sm);
  color: var(--accent-color);
  width: 18px;
  height: 18px;
}

/* Setting Items */
.setting-item {
  margin-bottom: var(--spacing-lg);
  transition: opacity 0.2s ease;
}

.setting-item:hover {
  opacity: 1;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-item label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  transition: color 0.2s ease;
}

.setting-item:hover label {
  color: var(--primary-color, #0078d4);
}

.setting-description {
  display: block;
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
  line-height: 1.4;
}

/* Enhanced Form Controls */
.form-control {
  width: 100%;
  max-width: 320px;
  padding: var(--spacing-md) var(--spacing-lg);
  border: 2px solid rgba(226, 232, 240, 0.6);
  border-radius: var(--border-radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.9) 100%
  );
  color: var(--text-primary);
  transition: all var(--transition-medium);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
  font-family: var(--font-family);
  backdrop-filter: blur(5px);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1),
    0 8px 25px rgba(99, 102, 241, 0.15), inset 0 2px 4px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px) scale(1.02);
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 1) 0%,
    rgba(240, 249, 255, 0.95) 100%
  );
}

.form-control:hover:not(:focus):not(:disabled) {
  border-color: var(--primary-light);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.form-control:disabled {
  background-color: var(--bg-disabled);
  color: var(--text-disabled);
  cursor: not-allowed;
  opacity: 0.6;
  border-color: var(--border-light);
}

/* Range Controls */
.range-control {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  max-width: 250px;
  height: 6px;
  margin-right: var(--spacing-md);
  background: linear-gradient(
    to right,
    var(--primary-color, #0078d4),
    var(--primary-light, #42a5f5)
  );
  border-radius: var(--border-radius-md);
  outline: none;
}

.range-control::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  background: var(--bg-primary, #ffffff);
  border: 2px solid var(--primary-color, #0078d4);
  border-radius: 50%;
  cursor: pointer;
  transition: transform 0.15s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.range-control::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: var(--bg-primary, #ffffff);
  border: 2px solid var(--primary-color, #0078d4);
  border-radius: 50%;
  cursor: pointer;
  transition: transform 0.15s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.range-control::-webkit-slider-thumb:hover,
.range-control::-moz-range-thumb:hover {
  transform: scale(1.2);
}

.range-value {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--primary-color, #0078d4);
  min-width: 45px;
  display: inline-block;
  text-align: center;
  background: var(--primary-light, #e6f2ff);
  border-radius: var(--border-radius-sm);
  padding: 2px 6px;
}

/* Color Controls */
.color-control {
  width: 60px;
  height: 40px;
  padding: 0;
  border: 2px solid var(--border-color, #e1e5e9);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  position: relative;
  overflow: hidden;
}

.color-control:hover {
  transform: scale(1.05);
  box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2);
}

.color-control:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.3);
}

/* Checkbox Controls */
.checkbox-item {
  display: flex;
  align-items: center;
}

.checkbox-label {
  display: flex !important;
  align-items: center;
  cursor: pointer;
  font-size: var(--font-size-sm) !important;
  margin: 0 !important;
  user-select: none;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 22px;
  height: 22px;
  border: 2px solid var(--border-color, #e1e5e9);
  border-radius: var(--border-radius-sm);
  margin-right: var(--spacing-sm);
  position: relative;
  transition: all 0.2s ease;
  flex-shrink: 0;
  background-color: var(--bg-primary, #ffffff);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.checkbox-label:hover .checkmark {
  border-color: var(--primary-color, #0078d4);
  box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.15);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background-color: var(--primary-color, #0078d4);
  border-color: var(--primary-color, #0078d4);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 6px;
  height: 12px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: translate(-60%, -60%) rotate(45deg);
}

/* Buttons */
.btn {
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  min-width: 100px;
  position: relative;
  overflow: hidden;
  font-family: "Inter", var(--font-family);
}

.btn:after {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  background-image: radial-gradient(circle, #fff 10%, transparent 10.01%);
  background-repeat: no-repeat;
  background-position: 50%;
  transform: scale(10, 10);
  opacity: 0;
  transition: transform 0.5s, opacity 1s;
}

.btn:active:after {
  transform: scale(0, 0);
  opacity: 0.3;
  transition: 0s;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  box-shadow: none;
}

.btn-primary {
  background-color: var(--primary-color, #0078d4);
  color: var(--bg-primary, #ffffff);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-hover, #106ebe);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.12), 0 2px 4px rgba(0, 0, 0, 0.08);
}

.btn-primary:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  background-color: var(--secondary-color, #6c757d);
  color: var(--bg-primary, #ffffff);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--secondary-hover, #5a6268);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.12), 0 2px 4px rgba(0, 0, 0, 0.08);
}

.btn-danger {
  background-color: var(--danger-color, #dc3545);
  color: var(--bg-primary, #ffffff);
}

.btn-danger:hover:not(:disabled) {
  background-color: var(--danger-hover, #c82333);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.12), 0 2px 4px rgba(0, 0, 0, 0.08);
}

/* Form Rows */
.form-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.form-row:last-child {
  margin-bottom: 0;
}

.form-row label {
  min-width: 120px;
  margin: 0;
}

/* Preset Management */
.presets-list {
  border: 1px solid var(--border-color, #e1e5e9);
  border-radius: var(--border-radius-md);
  max-height: 300px;
  overflow-y: auto;
  background: var(--bg-light, #f8f9fa);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.preset-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-light, #f1f3f4);
  transition: background-color 0.2s ease;
}

.preset-item:hover {
  background-color: var(--bg-hover, #e9ecef);
}

.preset-item:last-child {
  border-bottom: none;
}

.preset-info {
  flex: 1;
}

.preset-name {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.preset-details {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  background: var(--bg-primary, #ffffff);
  border-radius: var(--border-radius-sm);
  padding: 2px 6px;
  display: inline-block;
}

.preset-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.preset-form {
  border: 1px solid var(--border-color, #e1e5e9);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md);
  background: var(--bg-light, #f8f9fa);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.preset-settings {
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border-light, #f1f3f4);
}

/* Dimension Presets */
.preset-list {
  space-y: var(--spacing-sm);
}

.dimension-preset {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm);
  background: var(--bg-light, #f8f9fa);
  border: 1px solid var(--border-light, #f1f3f4);
  border-radius: var(--border-radius-sm);
  margin-bottom: var(--spacing-sm);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.dimension-preset:hover {
  transform: translateX(2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* File Input */
.file-input {
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-lg);
  border: 2px dashed var(--border-color, #e1e5e9);
  border-radius: var(--border-radius-md);
  background: var(--bg-light, #f8f9fa);
  cursor: pointer;
  transition: border-color 0.2s ease, background-color 0.2s ease;
  text-align: center;
  position: relative;
}

.file-input:hover {
  border-color: var(--primary-color, #0078d4);
  background-color: var(--primary-light, #e6f2ff);
}

.file-input::before {
  content: "📁 Select a file";
  display: block;
  font-weight: 500;
  margin-bottom: var(--spacing-xs);
  color: var(--primary-color, #0078d4);
}

/* Status Messages */
.status-message {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  z-index: 1000;
  opacity: 0;
  transform: translateX(100%) translateY(0);
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
  min-width: 250px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
}

.status-message::before {
  content: "";
  width: 20px;
  height: 20px;
  margin-right: var(--spacing-sm);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.status-message.show {
  opacity: 1;
  transform: translateX(0) translateY(0);
}

.status-message.success {
  background-color: var(--success-color, #28a745);
  color: white;
}

.status-message.success::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"></path></svg>');
}

.status-message.error {
  background-color: var(--danger-color, #dc3545);
  color: white;
}

.status-message.error::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"></path></svg>');
}

.status-message.info {
  background-color: var(--info-color, #17a2b8);
  color: white;
}

.status-message.info::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"></path></svg>');
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 1001;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal.show {
  display: flex;
  opacity: 1;
  animation: fadeIn 0.3s ease;
}

.modal-content {
  background: var(--bg-primary, #ffffff);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-xl);
  max-width: 420px;
  width: 90%;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  transform: scale(0.95);
  transition: transform 0.3s ease;
}

.modal.show .modal-content {
  transform: scale(1);
}

.modal-content h3 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-primary);
  font-size: var(--font-size-lg);
  border-bottom: 1px solid var(--border-light, #f1f3f4);
  padding-bottom: var(--spacing-sm);
}

.modal-content p {
  margin: 0 0 var(--spacing-lg) 0;
  color: var(--text-secondary);
  line-height: 1.6;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 120, 212, 0.4);
  }
  70% {
    box-shadow: 0 0 0 8px rgba(0, 120, 212, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 120, 212, 0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .settings-container {
    padding: var(--spacing-md);
  }

  .settings-nav {
    position: static;
    float: none;
    width: 100%;
    margin: 0 0 var(--spacing-lg) 0;
  }

  .nav-list {
    display: flex;
    overflow-x: auto;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs);
  }

  .nav-list li {
    margin: 0;
    flex-shrink: 0;
  }

  .nav-link {
    white-space: nowrap;
    padding: var(--spacing-xs) var(--spacing-sm);
  }

  .nav-link:before {
    display: none;
  }

  .nav-link svg {
    width: 16px;
    height: 16px;
  }

  .settings-content {
    margin-left: 0;
    padding: var(--spacing-md);
  }

  /* Enhanced Header Responsive */
  .settings-header {
    min-height: auto;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
    padding: var(--spacing-lg) var(--spacing-md);
    gap: var(--spacing-lg);
  }

  .title-section {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: center;
  }

  .title-icon {
    width: 48px;
    height: 48px;
  }

  .title-icon svg {
    width: 24px;
    height: 24px;
  }

  .title-text h1 {
    font-size: var(--font-size-xl);
    text-align: center;
  }

  .header-subtitle {
    text-align: center;
    font-size: var(--font-size-xs);
  }

  .header-actions {
    align-items: center;
    width: 100%;
  }

  .action-group {
    flex-direction: column;
    width: 100%;
    gap: var(--spacing-sm);
  }

  .header-actions .btn {
    width: 100%;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-md);
    min-height: 44px;
  }

  .btn-text {
    font-size: var(--font-size-xs);
  }

  .status-indicator {
    margin-top: var(--spacing-sm);
  }

  .floating-particles,
  .particle-system,
  .gradient-mesh,
  .geometric-pattern,
  .light-rays,
  .starfield {
    display: none; /* Hide complex effects on mobile for performance */
  }

  .icon-sparkles,
  .icon-orbital-ring,
  .wave-shimmer {
    display: none; /* Hide detailed animations on mobile */
  }

  .header-wave svg {
    height: 25px;
  }

  .form-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .form-row label {
    min-width: auto;
    margin-bottom: var(--spacing-xs);
  }

  .form-control {
    max-width: 100%;
  }

  .preset-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .preset-actions {
    margin-top: var(--spacing-sm);
    width: 100%;
    justify-content: flex-end;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: var(--spacing-md) var(--spacing-sm);
  }

  .title-icon {
    width: 40px;
    height: 40px;
  }

  .title-icon svg {
    width: 20px;
    height: 20px;
  }

  .title-text h1 {
    font-size: var(--font-size-lg);
  }

  .header-subtitle {
    font-size: 11px;
  }

  .action-group {
    gap: var(--spacing-xs);
  }

  .header-actions .btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    min-height: 40px;
  }

  .btn-icon svg {
    width: 14px;
    height: 14px;
  }

  .btn-text {
    font-size: 11px;
  }

  .status-indicator {
    padding: 4px 8px;
  }

  .status-text {
    font-size: 10px;
  }

  .status-dot {
    width: 6px;
    height: 6px;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .setting-group {
    border-width: 2px;
  }

  .nav-link.active {
    border: 2px solid currentColor;
  }

  .btn {
    border: 2px solid currentColor;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus Styles */
.nav-link:focus,
.btn:focus,
.form-control:focus {
  outline: 2px solid var(--primary-color, #0078d4);
  outline-offset: 2px;
}

/* Print Styles - Consolidated */
@media print {
  .settings-nav,
  .header-actions,
  .modal,
  .btn-support,
  .support-wave,
  .testimonial-nav {
    display: none !important;
  }

  .settings-content {
    margin-left: 0;
    box-shadow: none;
  }

  .settings-section,
  .testimonial {
    display: block !important;
    page-break-inside: avoid;
  }

  .setting-group,
  .about-card {
    box-shadow: none !important;
    border: 1px solid #000 !important;
    break-inside: avoid;
  }
}

/* About the Developer Styles */
.developer-info {
  display: flex;
  gap: var(--spacing-lg);
  align-items: flex-start;
}

.developer-avatar {
  flex-shrink: 0;
  position: relative;
}

.developer-photo {
  width: 100px;
  height: 100px;
  border: 3px solid var(--primary-color, #0078d4);
  border-radius: 50%;
  object-fit: cover;
  object-position: center;
  background: var(--bg-light, #f8f9fa);
  transition: transform 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.developer-avatar:hover .developer-photo {
  transform: scale(1.05) rotate(5deg);
}

.avatar-placeholder {
  width: 100px;
  height: 100px;
  background: linear-gradient(
    135deg,
    var(--primary-light, #e6f2ff),
    var(--primary-color, #0078d4)
  );
  border: 3px solid var(--primary-color, #0078d4);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--bg-primary, #ffffff);
  font-size: 36px;
  font-weight: bold;
}

.developer-details {
  flex: 1;
}

.developer-details h4 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  position: relative;
  display: inline-block;
}

.developer-details h4::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 50px;
  height: 3px;
  background: var(--primary-color, #0078d4);
  border-radius: var(--border-radius-md);
}

.developer-title {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--font-size-sm);
  color: var(--primary-color, #0078d4);
  font-weight: 500;
  background: var(--primary-light, #e6f2ff);
  padding: 2px 8px;
  border-radius: var(--border-radius-sm);
  display: inline-block;
}

.developer-bio {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-secondary);
  line-height: 1.6;
  font-size: 15px;
}

.developer-links {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.dev-link {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--bg-light, #f8f9fa);
  border: 1px solid var(--border-color, #e1e5e9);
  border-radius: var(--border-radius-sm);
  color: var(--text-primary);
  text-decoration: none;
  font-size: var(--font-size-xs);
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.dev-link:hover {
  background: var(--primary-color, #0078d4);
  color: white;
  border-color: var(--primary-color, #0078d4);
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.08);
}

.version-info {
  background: var(--bg-light, #f8f9fa);
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-sm);
  margin-top: var(--spacing-md);
}

.version-info p {
  margin: var(--spacing-xs) 0;
  color: var(--text-secondary);
}

.version-info strong {
  color: var(--text-primary);
}

.support-buttons {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
  margin-top: var(--spacing-md);
}

.support-buttons .btn {
  flex: 1;
  min-width: 120px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.acknowledgments-list {
  list-style: none;
  padding: 0;
  margin: var(--spacing-md) 0 0 0;
}

.acknowledgments-list li {
  padding: var(--spacing-xs) 0;
  color: var(--text-secondary);
  border-bottom: 1px solid var(--border-light, #f1f3f4);
  display: flex;
  align-items: center;
}

.acknowledgments-list li:before {
  content: "•";
  color: var(--primary-color, #0078d4);
  font-weight: bold;
  margin-right: var(--spacing-xs);
}

.acknowledgments-list li:last-child {
  border-bottom: none;
}

/* Responsive styles for developer section */
@media (max-width: 768px) {
  .developer-info {
    flex-direction: column;
    text-align: center;
  }

  .developer-details h4::after {
    left: 50%;
    transform: translateX(-50%);
  }

  .developer-links {
    justify-content: center;
  }

  .support-buttons {
    justify-content: center;
  }
}

/* Card Highlighting */
.setting-group:target {
  animation: pulse 1s;
  border-color: var(--primary-color, #0078d4);
  box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.2);
}

/* Select with custom dropdown indicator */
select.form-control {
  appearance: none;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6 9 12 15 18 9"></polyline></svg>');
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 16px;
  padding-right: 30px;
}

/* Utility Classes for Reuse */
.flex-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.flex-column {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.text-center {
  text-align: center;
}

.w-100 {
  width: 100%;
}

/* Animation Utilities */
.fade-in {
  animation: fadeIn 0.5s ease forwards;
}

.slide-in {
  animation: slideIn 0.5s ease forwards;
}

@keyframes slideIn {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Improved Accessibility Focus Styles */
.keyboard-focus:focus {
  outline: 2px solid var(--primary-color, #0078d4);
  outline-offset: 2px;
  box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2);
}

/* Enhanced Modern UI Enhancements */

/* Enhanced Card Animations */
@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(40px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* ===== ENHANCED TESTIMONIAL CAROUSEL ===== */

/* Testimonial Card Container */
.testimonial-card {
  position: relative;
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.98) 0%,
    rgba(248, 250, 252, 0.95) 100%
  );
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-xl);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(20px) saturate(180%);
  overflow: hidden;
  min-height: 400px;
}

.testimonial-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(
    90deg,
    #f59e0b 0%,
    #f97316 25%,
    #ef4444 50%,
    #ec4899 75%,
    #8b5cf6 100%
  );
  background-size: 200% 100%;
  animation: testimonialGradient 6s ease-in-out infinite;
}

@keyframes testimonialGradient {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Testimonial Carousel Container */
.testimonial-carousel {
  position: relative;
  width: 100%;
  height: 320px;
  overflow: hidden;
  border-radius: var(--border-radius-lg);
  margin-top: var(--spacing-lg);
  /* Enhanced performance optimizations */
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Individual Testimonial */
.testimonial {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: var(--spacing-xl) var(--spacing-lg);
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(248, 250, 252, 0.8) 100%
  );
  border-radius: var(--border-radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateX(100%) scale(0.95);
  opacity: 0;
  pointer-events: none;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08);
  /* Enhanced performance optimizations */
  will-change: transform, opacity;
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

.testimonial.active {
  transform: translateX(0) scale(1) !important;
  opacity: 1 !important;
  pointer-events: auto !important;
  filter: none !important;
  z-index: 2;
}

.testimonial.prev {
  transform: translateX(-100%) scale(0.9) !important;
  opacity: 0.3 !important;
  filter: blur(2px) !important;
  z-index: 1;
}

.testimonial.next {
  transform: translateX(100%) scale(0.9) !important;
  opacity: 0.3 !important;
  filter: blur(2px) !important;
  z-index: 1;
}

/* Testimonial Profile Section */
.testimonial-profile {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 2px solid rgba(99, 102, 241, 0.1);
}

.profile-avatar {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  font-weight: bold;
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
  position: relative;
  overflow: hidden;
}

.profile-avatar::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 70%
  );
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%,
  100% {
    transform: translateX(-100%) rotate(45deg);
  }
  50% {
    transform: translateX(100%) rotate(45deg);
  }
}

.profile-info {
  flex: 1;
}

.profile-name {
  display: block;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  line-height: 1.2;
}

.profile-title {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

/* Testimonial Text */
.testimonial-text {
  flex: 1;
  font-size: var(--font-size-lg);
  line-height: 1.6;
  color: var(--text-primary);
  text-align: center;
  margin: 0 0 var(--spacing-lg) 0;
  font-style: italic;
  position: relative;
  padding: 0 var(--spacing-md);
}

.testimonial-text::before {
  content: "" "";
  position: absolute;
  top: -10px;
  left: 0;
  font-size: 3rem;
  color: var(--primary-color);
  opacity: 0.3;
  font-family: serif;
}

.testimonial-text::after {
  content: "" "";
  position: absolute;
  bottom: -20px;
  right: 0;
  font-size: 3rem;
  color: var(--primary-color);
  opacity: 0.3;
  font-family: serif;
}

/* Testimonial Meta */
.testimonial-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid rgba(226, 232, 240, 0.6);
}

.verified-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: 4px 8px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.review-date {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  font-style: normal;
}

/* Testimonial Navigation */
.testimonial-nav {
  display: flex !important;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-lg);
  border-top: 1px solid rgba(226, 232, 240, 0.6);
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 10;
}

.testimonial-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(99, 102, 241, 0.4);
  cursor: pointer;
  transition: background 0.3s ease, transform 0.3s ease;
  position: relative;
  border: 2px solid transparent;
  outline: none;
  visibility: visible !important;
  opacity: 1 !important;
  display: block !important;
}

.testimonial-dot:hover {
  background: rgba(99, 102, 241, 0.7) !important;
  transform: scale(1.15);
  opacity: 1 !important;
  visibility: visible !important;
}

.testimonial-dot.active,
.testimonial-dot[aria-selected="true"] {
  background: var(--primary-color) !important;
  transform: scale(1.2);
  box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.3);
  opacity: 1 !important;
  visibility: visible !important;
}

.testimonial-dot:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Force navigation dots to always be visible */
.testimonial-nav,
.testimonial-nav *,
.testimonial-dot {
  visibility: visible !important;
  opacity: 1 !important;
  display: flex !important;
}

.testimonial-dot {
  display: block !important;
}

/* Dot Ripple Effect */
@keyframes dot-ripple {
  0% {
    transform: scale(0);
    opacity: 0.8;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* Carousel Controls */
.carousel-controls {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  padding: 0 var(--spacing-md);
  pointer-events: none;
  z-index: 3;
  transition: opacity 0.3s ease;
}

.testimonial-carousel.loading .carousel-controls {
  opacity: 0.5;
  pointer-events: none;
}

.testimonial-carousel.error .carousel-controls {
  opacity: 0.3;
}

.carousel-btn {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border: none;
  color: white;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
  pointer-events: auto;
  opacity: 0.8;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  will-change: transform, opacity;
  backface-visibility: hidden;
}

.carousel-btn:hover {
  opacity: 1;
  transform: scale(1.1);
  box-shadow: 0 12px 35px rgba(99, 102, 241, 0.4);
}

.carousel-btn:active {
  transform: scale(0.95);
}

.carousel-btn:focus {
  outline: 2px solid white;
  outline-offset: 2px;
}

.carousel-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none !important;
}

.carousel-btn:disabled:hover {
  transform: none !important;
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
}

.carousel-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 70%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.carousel-btn:hover::before {
  opacity: 1;
  animation: button-shine 0.6s ease;
}

@keyframes button-shine {
  0% {
    transform: translateX(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) rotate(45deg);
  }
}

/* Auto-play indicator */
.carousel-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: rgba(99, 102, 241, 0.2);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 100%);
  width: 0%;
  transition: width 0.1s linear;
  border-radius: inherit;
}

/* Responsive Design */
@media (max-width: 768px) {
  .testimonial-card {
    padding: var(--spacing-lg);
    min-height: 350px;
  }

  .testimonial-carousel {
    height: 280px;
  }

  .testimonial {
    padding: var(--spacing-lg) var(--spacing-md);
  }

  .profile-avatar {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }

  .profile-name {
    font-size: var(--font-size-md);
  }

  .testimonial-text {
    font-size: var(--font-size-md);
    padding: 0 var(--spacing-sm);
  }

  .testimonial-text::before,
  .testimonial-text::after {
    font-size: 2rem;
  }

  .carousel-btn {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }

  .carousel-controls {
    padding: 0 var(--spacing-sm);
  }
}

@media (max-width: 480px) {
  .testimonial-card {
    padding: var(--spacing-md);
    min-height: 320px;
  }

  .testimonial-carousel {
    height: 250px;
  }

  .testimonial-profile {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-sm);
  }

  .profile-avatar {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  .testimonial-text {
    font-size: var(--font-size-sm);
  }

  .testimonial-meta {
    flex-direction: column;
    gap: var(--spacing-xs);
    text-align: center;
  }

  .carousel-controls {
    display: none; /* Hide on very small screens */
  }
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
  .testimonial,
  .profile-avatar::before,
  .star,
  .testimonial-dot {
    animation: none !important;
    transition: none !important;
  }

  .testimonial-nav {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  .testimonial-dot {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .testimonial {
    border: 2px solid currentColor;
  }

  .testimonial-dot {
    border: 2px solid currentColor;
  }

  .carousel-btn {
    border: 2px solid white;
  }
}

/* Dark Theme Support */
[data-theme="dark"] .testimonial-card {
  background: linear-gradient(
    145deg,
    rgba(15, 23, 42, 0.95) 0%,
    rgba(30, 41, 59, 0.9) 100%
  );
  border-color: rgba(51, 65, 85, 0.6);
}

[data-theme="dark"] .testimonial {
  background: linear-gradient(
    135deg,
    rgba(30, 41, 59, 0.9) 0%,
    rgba(51, 65, 85, 0.8) 100%
  );
  border-color: rgba(51, 65, 85, 0.6);
}

[data-theme="dark"] .testimonial-text::before,
[data-theme="dark"] .testimonial-text::after {
  color: rgba(99, 102, 241, 0.6);
}

/* Enhanced About Card */
.about-card {
  position: relative;
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.98) 0%,
    rgba(248, 250, 252, 0.95) 100%
  );
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-xl);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(20px) saturate(180%);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  margin-bottom: var(--spacing-xl);
}

.about-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 32px 64px rgba(0, 0, 0, 0.15), 0 16px 32px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.about-card.interactive {
  cursor: pointer;
}

.about-card.interactive:active {
  transform: translateY(-4px) scale(1.01);
}

.card-content {
  position: relative;
  z-index: 2;
}

.card-content h3 {
  margin: 0 0 var(--spacing-lg) 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding-bottom: var(--spacing-md);
  border-bottom: 2px solid rgba(99, 102, 241, 0.1);
}

/* Fade in animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.setting-group {
  animation: cardSlideIn 0.6s var(--transition-spring) backwards;
}

.setting-group:nth-child(1) {
  animation-delay: 0.1s;
}
.setting-group:nth-child(2) {
  animation-delay: 0.2s;
}
.setting-group:nth-child(3) {
  animation-delay: 0.3s;
}
.setting-group:nth-child(4) {
  animation-delay: 0.4s;
}

/* Interactive Floating Elements */
.floating-orb {
  position: absolute;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: radial-gradient(
    circle,
    rgba(99, 102, 241, 0.1) 0%,
    transparent 70%
  );
  pointer-events: none;
  animation: float 6s ease-in-out infinite;
}

.floating-orb:nth-child(1) {
  top: 10%;
  right: 10%;
  animation-delay: 0s;
}

.floating-orb:nth-child(2) {
  bottom: 20%;
  left: 5%;
  animation-delay: 2s;
  width: 150px;
  height: 150px;
  background: radial-gradient(
    circle,
    rgba(139, 92, 246, 0.08) 0%,
    transparent 70%
  );
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(2deg);
  }
  66% {
    transform: translateY(10px) rotate(-1deg);
  }
}

/* Smart Grid Layouts */
.setting-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.setting-card {
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.8) 100%
  );
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all var(--transition-medium);
  border: 1px solid rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(10px);
}

.setting-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

/* Enhanced Toggle Switches */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  transition: all var(--transition-medium);
  border-radius: 34px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  transition: all var(--transition-medium);
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.toggle-switch input:checked + .toggle-slider {
  background: var(--gradient-primary);
  box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
}

.toggle-switch input:checked + .toggle-slider:before {
  transform: translateX(26px);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
}

/* Progressive Loading States */
.skeleton {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.1) 25%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0.1) 75%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: var(--border-radius-md);
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.skeleton-text {
  height: 16px;
  margin-bottom: 8px;
}

.skeleton-text:last-child {
  width: 60%;
}

/* Interactive Tooltips */
.tooltip {
  position: relative;
  cursor: help;
}

.tooltip .tooltiptext {
  visibility: hidden;
  width: 200px;
  background: rgba(15, 23, 42, 0.95);
  color: white;
  text-align: center;
  border-radius: var(--border-radius-md);
  padding: 8px 12px;
  position: absolute;
  z-index: var(--z-tooltip);
  bottom: 125%;
  left: 50%;
  margin-left: -100px;
  opacity: 0;
  transition: opacity var(--transition-medium);
  backdrop-filter: blur(10px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  font-size: var(--font-size-xs);
  line-height: 1.4;
}

.tooltip .tooltiptext::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: rgba(15, 23, 42, 0.95) transparent transparent transparent;
}

.tooltip:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}

/* Enhanced Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: 4px 8px;
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.status-indicator.online {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-indicator.offline {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-indicator::before {
  content: "";
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
  animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Enhanced Dark Mode Support */
[data-theme="dark"] .setting-group {
  background: linear-gradient(
    145deg,
    rgba(30, 41, 59, 0.9) 0%,
    rgba(51, 65, 85, 0.8) 100%
  );
  border-color: rgba(51, 65, 85, 0.6);
}

[data-theme="dark"] .form-control {
  background: linear-gradient(
    145deg,
    rgba(30, 41, 59, 0.95) 0%,
    rgba(51, 65, 85, 0.9) 100%
  );
  border-color: rgba(51, 65, 85, 0.8);
  color: var(--text-primary);
}

[data-theme="dark"] .settings-header,
[data-theme="dark"] .settings-nav,
[data-theme="dark"] .settings-content {
  background: linear-gradient(
    145deg,
    rgba(15, 23, 42, 0.95) 0%,
    rgba(30, 41, 59, 0.9) 100%
  );
  border-color: rgba(51, 65, 85, 0.6);
}

/* Accessibility Enhancements */
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus-trap {
  position: relative;
}

.focus-trap:focus-within {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
  border-radius: var(--border-radius-md);
}

/* Enhanced Mobile Experience */
@media (max-width: 480px) {
  .settings-container {
    padding: var(--spacing-sm);
  }

  .settings-header {
    padding: var(--spacing-md);
    flex-direction: column;
    text-align: center;
  }

  .settings-header h1 {
    font-size: var(--font-size-xl);
  }

  .header-actions {
    width: 100%;
    justify-content: center;
  }

  .setting-group {
    padding: var(--spacing-md);
  }
}

/* Performance Optimizations */
.settings-section:not(.active) {
  display: none;
  visibility: hidden;
}

.setting-group,
.nav-link,
.btn {
  contain: layout style paint;
}

/* Enhanced Print Styles */
@media print {
  .floating-orb,
  .tooltip,
  .status-indicator {
    display: none !important;
  }

  .setting-group {
    break-inside: avoid;
    margin-bottom: var(--spacing-md);
  }
}

/* Color Theme Variations */
.theme-blue {
  --primary-color: #3b82f6;
  --primary-hover: #2563eb;
}
.theme-purple {
  --primary-color: #8b5cf6;
  --primary-hover: #7c3aed;
}
.theme-green {
  --primary-color: #10b981;
  --primary-hover: #059669;
}
.theme-orange {
  --primary-color: #f59e0b;
  --primary-hover: #d97706;
}

/* Enhanced Loading States */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: inherit;
  backdrop-filter: blur(5px);
  z-index: 10;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(99, 102, 241, 0.2);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Enhanced Version Card Styles */
/* Simple & Clean Version Card */
.version-card-simple {
  transition: all 0.3s ease;
}

.version-card-simple:hover {
  transform: translateY(-4px);
}

.version-card-simple h3 {
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

/* Version Main Info */
.version-main-info {
  margin-bottom: var(--spacing-lg);
}

.version-number {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--border-light);
}

.version-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.version-value {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.version-status {
  padding: 2px 8px;
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

/* Version Details */
.version-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xs) 0;
}

.detail-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.detail-value {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

.status-active {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: #059669 !important;
}

.status-dot-simple {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

/* Simple Progress Bar */
.version-progress-simple {
  margin: var(--spacing-lg) 0;
  padding: var(--spacing-md);
  background: rgba(248, 250, 252, 0.8);
  border-radius: var(--border-radius-md);
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.progress-label-simple {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.progress-label-simple span:first-child {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.progress-value-simple {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
}

.progress-bar-simple {
  width: 100%;
  height: 8px;
  background: rgba(226, 232, 240, 0.6);
  border-radius: var(--border-radius-full);
  overflow: hidden;
}

.progress-fill-simple {
  height: 100%;
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
  border-radius: var(--border-radius-full);
  transition: width 1.5s ease;
  position: relative;
}

.progress-fill-simple::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 100%
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Simple Action Buttons */
.version-actions-simple {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
}

.btn-simple {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn-primary-simple {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.btn-primary-simple:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
}

.btn-secondary-simple {
  background: rgba(248, 250, 252, 0.8);
  color: var(--text-primary);
  border: 1px solid rgba(226, 232, 240, 0.8);
}

.btn-secondary-simple:hover {
  background: rgba(226, 232, 240, 0.4);
  transform: translateY(-1px);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .version-number {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .version-actions-simple {
    flex-direction: column;
  }

  .btn-simple {
    justify-content: center;
  }
}

/* Cleaned up old version card styles - using simple version now */

@keyframes iconFloat {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-3px) rotate(1deg);
  }
  50% {
    transform: translateY(0px) rotate(0deg);
  }
  75% {
    transform: translateY(-2px) rotate(-1deg);
  }
}

.version-icon svg {
  width: 28px;
  height: 28px;
  color: white;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.version-header-text h3 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.version-subtitle {
  margin: 0;
  font-size: var(--font-size-sm);
  color: rgba(255, 255, 255, 0.9);
  font-weight: var(--font-weight-medium);
}

.version-badge {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-md);
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--border-radius-full);
  backdrop-filter: blur(10px);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  color: white;
  animation: badgePulse 3s ease-in-out infinite;
}

@keyframes badgePulse {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(255, 255, 255, 0);
  }
}

.status-dot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.6);
  animation: statusPulse 2s ease-in-out infinite;
}

/* Version Card Body - Compact */
.version-card-body {
  padding: var(--spacing-lg); /* Reduced padding */
  max-height: 400px; /* Limit body height */
  overflow-y: auto; /* Enable scrolling if needed */
}

.version-stats-grid {
  display: grid;
  grid-template-columns: repeat(
    2,
    1fr
  ); /* Fixed 2 columns for compact layout */
  gap: var(--spacing-md); /* Reduced gap */
  margin-bottom: var(--spacing-lg); /* Reduced margin */
}

.version-stat {
  position: relative;
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(248, 250, 252, 0.6) 100%
  );
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-md); /* Reduced padding */
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.version-stat:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.15);
  border-color: rgba(99, 102, 241, 0.3);
}

.stat-icon {
  width: 32px; /* Smaller icon */
  height: 32px;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border-radius: var(--border-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-sm); /* Reduced margin */
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.stat-icon svg {
  width: 20px;
  height: 20px;
  color: white;
}

.stat-label {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-xs);
}

.stat-value {
  display: block;
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  font-weight: var(--font-weight-bold);
}

/* Enhanced Progress Section - Compact */
.version-progress-section {
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(248, 250, 252, 0.7) 100%
  );
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-md); /* Reduced padding */
  margin-bottom: var(--spacing-md); /* Reduced margin */
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm); /* Reduced margin */
}

/* Update Toggle Button */
.update-toggle {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.3);
  border-radius: var(--border-radius-md);
  color: var(--primary-color);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
}

.update-toggle:hover {
  background: rgba(99, 102, 241, 0.2);
  transform: translateY(-1px);
}

/* Compact Timeline Styles */
.update-history-compact {
  margin-top: var(--spacing-sm);
  padding-top: var(--spacing-sm);
  border-top: 1px solid rgba(226, 232, 240, 0.6);
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.timeline-compact {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.timeline-item-compact {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  background: rgba(255, 255, 255, 0.6);
  border-radius: var(--border-radius-md);
  transition: all 0.2s ease;
}

.timeline-item-compact:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateX(4px);
}

.timeline-version-compact {
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  font-size: var(--font-size-xs);
  min-width: 40px;
}

.timeline-date-compact {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  min-width: 70px;
}

.timeline-summary {
  font-size: var(--font-size-xs);
  color: var(--text-primary);
  flex: 1;
}

.progress-title {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.progress-percentage {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
}

.enhanced-progress-bar {
  position: relative;
  width: 100%;
  height: 12px;
  background: linear-gradient(
    90deg,
    rgba(226, 232, 240, 0.5) 0%,
    rgba(203, 213, 225, 0.5) 100%
  );
  border-radius: var(--border-radius-full);
  overflow: hidden;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.enhanced-progress-fill {
  position: relative;
  height: 100%;
  background: linear-gradient(90deg, #10b981 0%, #059669 50%, #047857 100%);
  border-radius: inherit;
  transition: width 2s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.enhanced-progress-fill::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 100%
  );
  animation: progressShine 2s ease-in-out infinite;
}

@keyframes progressShine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Update History Section */
.update-history {
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(248, 250, 252, 0.7) 100%
  );
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.update-history h4 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.timeline {
  position: relative;
  padding-left: var(--spacing-xl);
}

.timeline::before {
  content: "";
  position: absolute;
  left: 16px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(
    180deg,
    var(--primary-color) 0%,
    rgba(99, 102, 241, 0.3) 100%
  );
}

.timeline-item {
  position: relative;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background: rgba(255, 255, 255, 0.8);
  border-radius: var(--border-radius-md);
  border: 1px solid rgba(226, 232, 240, 0.6);
  transition: all 0.3s ease;
}

.timeline-item:hover {
  transform: translateX(8px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
}

.timeline-item::before {
  content: "";
  position: absolute;
  left: -24px;
  top: 20px;
  width: 12px;
  height: 12px;
  background: var(--primary-color);
  border: 3px solid white;
  border-radius: 50%;
  box-shadow: 0 0 0 2px var(--primary-color);
}

.timeline-version {
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.timeline-date {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.timeline-changes {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  line-height: 1.5;
}

/* Action Buttons */
.version-actions {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.version-btn {
  flex: 1;
  min-width: 140px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  border-radius: var(--border-radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.version-btn-primary {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
}

.version-btn-secondary {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  color: var(--text-primary);
  border: 1px solid rgba(226, 232, 240, 0.8);
}

.version-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
}

.version-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.version-btn:hover::before {
  left: 100%;
}

/* Responsive Design - Compact */
@media (max-width: 768px) {
  .version-card {
    max-height: 500px; /* Smaller on mobile */
  }

  .version-card-header {
    padding: var(--spacing-md) var(--spacing-lg);
  }

  .version-header-content {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-sm);
  }

  .version-title-section {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .version-icon {
    width: 40px;
    height: 40px;
  }

  .version-stats-grid {
    grid-template-columns: 1fr; /* Single column on mobile */
  }

  .version-actions {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .version-btn {
    min-width: auto;
    padding: var(--spacing-sm);
  }

  .timeline-item-compact {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .timeline-version-compact,
  .timeline-date-compact {
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .version-card-body {
    padding: var(--spacing-lg);
  }

  .version-header-text h3 {
    font-size: var(--font-size-lg);
  }

  .stat-value {
    font-size: var(--font-size-md);
  }
}

/* Animation on load */
.version-card {
  animation: versionCardEntry 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes versionCardEntry {
  0% {
    opacity: 0;
    transform: translateY(40px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Dark theme support */
[data-theme="dark"] .version-card {
  background: linear-gradient(
    145deg,
    rgba(30, 41, 59, 0.95) 0%,
    rgba(51, 65, 85, 0.9) 50%,
    rgba(30, 41, 59, 0.95) 100%
  );
  border-color: rgba(51, 65, 85, 0.6);
}

[data-theme="dark"] .version-stat {
  background: linear-gradient(
    145deg,
    rgba(51, 65, 85, 0.6) 0%,
    rgba(71, 85, 105, 0.4) 100%
  );
  border-color: rgba(71, 85, 105, 0.6);
}

[data-theme="dark"] .version-progress-section,
[data-theme="dark"] .update-history {
  background: linear-gradient(
    145deg,
    rgba(51, 65, 85, 0.6) 0%,
    rgba(71, 85, 105, 0.4) 100%
  );
  border-color: rgba(71, 85, 105, 0.6);
}

[data-theme="dark"] .timeline-item {
  background: rgba(51, 65, 85, 0.6);
  border-color: rgba(71, 85, 105, 0.6);
}

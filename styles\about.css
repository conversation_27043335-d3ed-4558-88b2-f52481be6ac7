/* About Page Enhanced Styles - Enhanced for Beauty & User Experience */

/* Enhanced <PERSON><PERSON> Styles with Improved Typography */
.about-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg) 0;
  position: relative;
}

.about-header::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent,
    var(--primary-color),
    #4b96d7,
    transparent
  );
  border-radius: 1px;
  animation: gradient-flow 3s ease-in-out infinite;
}

@keyframes gradient-flow {
  0%,
  100% {
    transform: scaleX(0.5);
    opacity: 0.5;
  }
  50% {
    transform: scaleX(1);
    opacity: 1;
  }
}

.about-header h2 {
  margin: 0;
  background: linear-gradient(135deg, var(--primary-color), #4b96d7, #6366f1);
  background-size: 300% auto;
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-size: 2.2rem;
  font-weight: 700;
  letter-spacing: -0.8px;
  position: relative;
  animation: gradient-shift 4s ease infinite;
}

.about-header h2::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(99, 102, 241, 0.1),
    rgba(75, 150, 215, 0.1)
  );
  border-radius: 8px;
  filter: blur(20px);
  z-index: -1;
  animation: glow-pulse 2s ease-in-out infinite alternate;
}

@keyframes glow-pulse {
  from {
    opacity: 0.5;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.version-badge {
  background: linear-gradient(
    135deg,
    rgba(0, 120, 212, 0.1),
    rgba(75, 150, 215, 0.15)
  );
  color: var(--primary-color);
  font-weight: 600;
  border-radius: var(--border-radius-lg);
  padding: 8px 16px;
  font-size: 0.9rem;
  border: 1px solid rgba(0, 120, 212, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.version-badge::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.6s ease;
}

.version-badge:hover::before {
  left: 100%;
}

.version-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 120, 212, 0.25);
  border-color: var(--primary-color);
}

/* Enhanced Card Designs with Modern Aesthetics */
.about-card {
  background: var(--bg-primary);
  border-radius: 16px;
  border: 1px solid var(--border-color);
  overflow: hidden;
  margin-bottom: var(--spacing-lg);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(10px);
}

.about-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), #4b96d7, #6366f1);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.about-card:hover::before {
  opacity: 1;
}

.about-card.interactive {
  cursor: pointer;
}

.about-card.interactive:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border-color: rgba(0, 120, 212, 0.3);
}

.about-card .card-content {
  padding: var(--spacing-xl);
  position: relative;
}

.about-card h3 {
  margin-top: 0;
  margin-bottom: var(--spacing-md);
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--text-primary);
  position: relative;
  letter-spacing: -0.3px;
}

/* Enhanced Card Row Layout */
.about-card-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
}

.about-card-row .about-card {
  margin-bottom: 0;
}

/* Enhanced Feature Highlight Card */
.feature-highlight-card {
  background: linear-gradient(
    135deg,
    rgba(99, 102, 241, 0.05) 0%,
    rgba(75, 150, 215, 0.08) 50%,
    var(--bg-primary) 100%
  );
  border: 1px solid rgba(99, 102, 241, 0.15);
  position: relative;
  overflow: hidden;
}

.feature-highlight-card::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(99, 102, 241, 0.1) 0%,
    transparent 70%
  );
  animation: rotate-slow 20s linear infinite;
  pointer-events: none;
}

@keyframes rotate-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.feature-description {
  color: var(--text-secondary);
  line-height: 1.7;
  margin-bottom: var(--spacing-lg);
  font-size: 1.05rem;
}

.feature-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.stat-box {
  background: linear-gradient(
    135deg,
    rgba(0, 120, 212, 0.05),
    rgba(75, 150, 215, 0.08)
  );
  padding: var(--spacing-lg);
  border-radius: 12px;
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 6px;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 120, 212, 0.1);
  position: relative;
  overflow: hidden;
}

.stat-box::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(99, 102, 241, 0.1),
    rgba(75, 150, 215, 0.1)
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-box:hover::before {
  opacity: 1;
}

.stat-box:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 120, 212, 0.15);
  border-color: rgba(0, 120, 212, 0.3);
}

.stat-number {
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--primary-color);
  position: relative;
  z-index: 1;
}

.stat-label {
  font-size: 0.85rem;
  color: var(--text-secondary);
  font-weight: 500;
  position: relative;
  z-index: 1;
}

/* Enhanced Developer Profile Card */
.dev-profile-card {
  border: none;
  background: linear-gradient(
    135deg,
    var(--bg-primary) 0%,
    rgba(99, 102, 241, 0.02) 100%
  );
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.developer-info {
  display: flex;
  gap: var(--spacing-xl);
  align-items: flex-start;
}

.developer-avatar {
  position: relative;
  width: 120px;
  height: 120px;
  flex-shrink: 0;
}

.avatar-ring {
  position: absolute;
  top: -6px;
  left: -6px;
  right: -6px;
  bottom: -6px;
  border-radius: 50%;
  border: 3px solid transparent;
  background: linear-gradient(
    45deg,
    var(--primary-color),
    #4b96d7,
    #6366f1,
    var(--primary-color)
  );
  background-size: 400% 400%;
  animation: gradient-rotate 8s ease infinite;
  opacity: 0.7;
}

@keyframes gradient-rotate {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.developer-photo {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid white;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.developer-avatar:hover .developer-photo {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 120, 212, 0.3);
}

.developer-details {
  flex: 1;
}

.developer-details h3 {
  margin-bottom: var(--spacing-sm);
  color: var(--text-secondary);
  font-size: 1rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.developer-details h4 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-primary);
  position: relative;
  letter-spacing: -0.5px;
}

.developer-details h4::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), #4b96d7);
  border-radius: 2px;
  animation: expand-contract 2s ease-in-out infinite;
}

@keyframes expand-contract {
  0%,
  100% {
    width: 60px;
  }
  50% {
    width: 80px;
  }
}

.developer-title {
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  font-size: 1.1rem;
  letter-spacing: 0.3px;
}

.developer-bio {
  color: var(--text-secondary);
  line-height: 1.7;
  margin-bottom: var(--spacing-lg);
  font-size: 1rem;
}

.developer-links {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.dev-link {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text-secondary);
  text-decoration: none;
  padding: 8px 16px;
  border-radius: 8px;
  background: rgba(0, 120, 212, 0.05);
  border: 1px solid rgba(0, 120, 212, 0.1);
  transition: all 0.3s ease;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.dev-link::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(0, 120, 212, 0.1),
    transparent
  );
  transition: left 0.6s ease;
}

.dev-link:hover::before {
  left: 100%;
}

.dev-link:hover {
  color: var(--primary-color);
  background: rgba(0, 120, 212, 0.1);
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 120, 212, 0.2);
}

.dev-link svg {
  transition: all 0.3s ease;
}

.dev-link:hover svg {
  transform: scale(1.1) rotate(5deg);
}

/* Enhanced Version Information */
.version-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.version-info p {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-secondary);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.version-info strong {
  color: var(--text-primary);
  font-weight: 600;
}

.version-progress {
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md);
  background: rgba(0, 120, 212, 0.03);
  border-radius: 8px;
  border: 1px solid rgba(0, 120, 212, 0.1);
}

.progress-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
  font-size: 0.9rem;
  font-weight: 600;
}

.progress-bar {
  height: 8px;
  background: rgba(0, 120, 212, 0.1);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), #4b96d7);
  border-radius: 4px;
  transition: width 1s ease;
  position: relative;
  overflow: hidden;
}

.progress-fill::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Enhanced Testimonial Carousel with Advanced Swap Animations */
.testimonial-carousel {
  position: relative;
  min-height: 180px;
  overflow: hidden;
  border-radius: 12px;
  background: linear-gradient(
    135deg,
    rgba(0, 120, 212, 0.02),
    rgba(75, 150, 215, 0.05)
  );
  padding: var(--spacing-lg);
  perspective: 1000px;
  transform-style: preserve-3d;
}

/* Enhanced Testimonial States with 3D Transformations */
.testimonial {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  transform: translateX(100%) rotateY(45deg) scale(0.8);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  justify-content: center;
  transform-origin: center center;
  backface-visibility: hidden;
  will-change: transform, opacity;
  filter: blur(3px) brightness(0.7);
}

.testimonial.active {
  opacity: 1;
  transform: translateX(0) rotateY(0deg) scale(1);
  filter: blur(0px) brightness(1);
  z-index: 10;
}

.testimonial.entering {
  animation: testimonialEnter 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.testimonial.exiting {
  animation: testimonialExit 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.testimonial.slide-left {
  transform: translateX(-100%) rotateY(-45deg) scale(0.8);
}

.testimonial.slide-right {
  transform: translateX(100%) rotateY(45deg) scale(0.8);
}

/* Advanced Keyframe Animations */
@keyframes testimonialEnter {
  0% {
    opacity: 0;
    transform: translateX(100%) rotateY(45deg) scale(0.8) translateZ(-50px);
    filter: blur(8px) brightness(0.5);
  }
  25% {
    opacity: 0.3;
    transform: translateX(50%) rotateY(25deg) scale(0.9) translateZ(-25px);
    filter: blur(4px) brightness(0.7);
  }
  50% {
    opacity: 0.7;
    transform: translateX(20%) rotateY(10deg) scale(0.95) translateZ(-10px);
    filter: blur(2px) brightness(0.85);
  }
  75% {
    opacity: 0.9;
    transform: translateX(5%) rotateY(2deg) scale(0.98) translateZ(-2px);
    filter: blur(1px) brightness(0.95);
  }
  100% {
    opacity: 1;
    transform: translateX(0) rotateY(0deg) scale(1) translateZ(0px);
    filter: blur(0px) brightness(1);
  }
}

@keyframes testimonialExit {
  0% {
    opacity: 1;
    transform: translateX(0) rotateY(0deg) scale(1) translateZ(0px);
    filter: blur(0px) brightness(1);
  }
  25% {
    opacity: 0.9;
    transform: translateX(-5%) rotateY(-2deg) scale(0.98) translateZ(-2px);
    filter: blur(1px) brightness(0.95);
  }
  50% {
    opacity: 0.7;
    transform: translateX(-20%) rotateY(-10deg) scale(0.95) translateZ(-10px);
    filter: blur(2px) brightness(0.85);
  }
  75% {
    opacity: 0.3;
    transform: translateX(-50%) rotateY(-25deg) scale(0.9) translateZ(-25px);
    filter: blur(4px) brightness(0.7);
  }
  100% {
    opacity: 0;
    transform: translateX(-100%) rotateY(-45deg) scale(0.8) translateZ(-50px);
    filter: blur(8px) brightness(0.5);
  }
}

/* Fade and Scale Animation Variants */
@keyframes testimonialFadeSlide {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    filter: blur(5px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0px);
  }
}

@keyframes testimonialFlip {
  0% {
    opacity: 0;
    transform: rotateX(90deg) scale(0.8);
  }
  50% {
    opacity: 0.5;
    transform: rotateX(45deg) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: rotateX(0deg) scale(1);
  }
}

/* Enhanced Profile Elements */
.testimonial-profile {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm);
  background: rgba(0, 120, 212, 0.03);
  border-radius: 12px;
  border: 1px solid rgba(0, 120, 212, 0.1);
  transition: all 0.4s ease;
  transform: translateY(10px);
  opacity: 0;
}

.testimonial.active .testimonial-profile {
  animation: profileSlideIn 0.6s ease 0.2s forwards;
}

@keyframes profileSlideIn {
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.profile-avatar {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--primary-color), #4b96d7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 120, 212, 0.2);
  position: relative;
  overflow: hidden;
  transition: all 0.4s ease;
}

.testimonial.active .profile-avatar {
  animation: avatarBounce 0.6s ease 0.4s;
}

@keyframes avatarBounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0) scale(1);
  }
  40% {
    transform: translateY(-5px) scale(1.05);
  }
  60% {
    transform: translateY(-3px) scale(1.02);
  }
}

.profile-avatar::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: avatar-shine 3s ease-in-out infinite;
  opacity: 0;
}

.testimonial.active .profile-avatar::before {
  opacity: 1;
}

@keyframes avatar-shine {
  0%,
  100% {
    transform: rotate(0deg);
    opacity: 0;
  }
  50% {
    transform: rotate(180deg);
    opacity: 1;
  }
}

/* Enhanced Text Elements */
.testimonial-text {
  font-style: italic;
  color: var(--text-primary);
  text-align: center;
  margin-bottom: var(--spacing-md);
  font-size: 1.05rem;
  line-height: 1.6;
  position: relative;
  transform: translateY(20px);
  opacity: 0;
  transition: all 0.6s ease;
}

.testimonial.active .testimonial-text {
  animation: textReveal 0.8s ease 0.3s forwards;
}

@keyframes textReveal {
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  50% {
    transform: translateY(-5px);
    opacity: 0.7;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.testimonial-text::before,
.testimonial-text::after {
  content: '"';
  font-size: 2rem;
  color: var(--primary-color);
  opacity: 0.3;
  position: absolute;
  font-family: serif;
  transition: all 0.4s ease;
}

.testimonial.active .testimonial-text::before,
.testimonial.active .testimonial-text::after {
  animation: quoteGlow 1s ease 0.5s;
}

@keyframes quoteGlow {
  0%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
    text-shadow: 0 0 10px var(--primary-color);
  }
}

.testimonial-text::before {
  top: -10px;
  left: -15px;
}

.testimonial-text::after {
  bottom: -25px;
  right: -10px;
}

/* Enhanced Meta Information */
.testimonial-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-sm);
  border-top: 1px solid rgba(0, 120, 212, 0.1);
  font-size: 0.8rem;
  transform: translateY(15px);
  opacity: 0;
}

.testimonial.active .testimonial-meta {
  animation: metaSlideUp 0.6s ease 0.6s forwards;
}

@keyframes metaSlideUp {
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.verified-badge {
  background: linear-gradient(135deg, #10b981, #34d399);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 4px;
  transform: scale(0.8);
  transition: all 0.3s ease;
}

.testimonial.active .verified-badge {
  animation: badgePop 0.4s ease 0.8s forwards;
}

@keyframes badgePop {
  0% {
    transform: scale(0.8);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* Enhanced Navigation Dots */
.testimonial-nav {
  display: flex;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
  position: absolute;
  bottom: var(--spacing-md);
  left: 50%;
  transform: translateX(-50%);
}

.testimonial-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(0, 120, 212, 0.3);
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  border: 2px solid transparent;
  overflow: hidden;
}

.testimonial-dot::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, var(--primary-color), transparent);
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
  border-radius: 50%;
}

.testimonial-dot:hover {
  background: rgba(0, 120, 212, 0.6);
  transform: scale(1.3);
  box-shadow: 0 0 15px rgba(0, 120, 212, 0.4);
}

.testimonial-dot:hover::before {
  width: 20px;
  height: 20px;
  opacity: 0.3;
}

.testimonial-dot.active {
  background: var(--primary-color);
  transform: scale(1.4);
  box-shadow: 0 4px 12px rgba(0, 120, 212, 0.5);
}

.testimonial-dot.active::before {
  width: 25px;
  height: 25px;
  opacity: 0.2;
  animation: dotPulse 2s ease-in-out infinite;
}

@keyframes dotPulse {
  0%,
  100% {
    width: 25px;
    height: 25px;
    opacity: 0.2;
  }
  50% {
    width: 35px;
    height: 35px;
    opacity: 0.1;
  }
}

/* Enhanced Carousel Controls */
.carousel-controls {
  position: absolute;
  bottom: 60px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  pointer-events: none;
  z-index: 20;
  padding: 0 var(--spacing-md);
}

.carousel-btn {
  background: rgba(0, 120, 212, 0.9);
  color: white;
  border: none;
  width: 45px;
  height: 45px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.3rem;
  font-weight: bold;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  pointer-events: auto;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(0, 120, 212, 0.3);
  opacity: 0;
  transform: scale(0.7) translateY(10px);
  position: relative;
  overflow: hidden;
}

.carousel-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.6s ease;
}

.testimonial-carousel:hover .carousel-btn {
  opacity: 1;
  transform: scale(1) translateY(0);
}

.carousel-btn:hover {
  background: var(--primary-color);
  transform: scale(1.15) translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 120, 212, 0.4);
}

.carousel-btn:hover::before {
  left: 100%;
}

.carousel-btn:active {
  transform: scale(1.05) translateY(0);
}

/* Enhanced Progress Bar */
.carousel-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: rgba(99, 102, 241, 0.2);
  overflow: hidden;
  border-radius: 0 0 12px 12px;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 50%, #06b6d4 100%);
  width: 0%;
  transition: width 0.1s linear;
  border-radius: inherit;
  position: relative;
  overflow: hidden;
}

.progress-bar::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: progressShimmer 2s ease-in-out infinite;
}

@keyframes progressShimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Testimonial Card Hover Effects */
.testimonial-card {
  transition: all 0.4s ease;
}

.testimonial-card:hover .testimonial-carousel {
  transform: scale(1.02);
}

.testimonial-card:hover .testimonial.active {
  transform: translateX(0) rotateY(0deg) scale(1.02);
}

/* Animation Variants for Different Directions */
.testimonial.slide-from-right {
  transform: translateX(100%) rotateY(45deg) scale(0.8);
}

.testimonial.slide-from-left {
  transform: translateX(-100%) rotateY(-45deg) scale(0.8);
}

.testimonial.slide-from-top {
  transform: translateY(-50px) rotateX(45deg) scale(0.8);
}

.testimonial.slide-from-bottom {
  transform: translateY(50px) rotateX(-45deg) scale(0.8);
}

/* Performance Optimizations */
.testimonial-carousel,
.testimonial,
.carousel-btn,
.testimonial-dot {
  will-change: transform, opacity;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .testimonial,
  .testimonial-profile,
  .testimonial-text,
  .testimonial-meta,
  .verified-badge,
  .carousel-btn,
  .testimonial-dot {
    animation: none !important;
    transition: opacity 0.3s ease !important;
  }

  .testimonial {
    transform: none !important;
  }

  .testimonial:not(.active) {
    opacity: 0;
  }

  .testimonial.active {
    opacity: 1;
  }
}

/* Ultra Modern Support Card with Glassmorphism */
.support-card {
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  overflow: hidden;
  padding: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.05);
}

.support-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px rgba(0, 120, 212, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* Animated Background Elements */
.support-bg-animation {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  pointer-events: none;
}

.floating-shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(
    135deg,
    rgba(0, 120, 212, 0.1),
    rgba(99, 102, 241, 0.1)
  );
  animation: float-shapes 20s ease-in-out infinite;
}

.shape-1 {
  width: 100px;
  height: 100px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 60px;
  height: 60px;
  top: 60%;
  right: 20%;
  animation-delay: 7s;
}

.shape-3 {
  width: 80px;
  height: 80px;
  bottom: 20%;
  left: 60%;
  animation-delay: 14s;
}

@keyframes float-shapes {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  25% {
    transform: translateY(-20px) rotate(90deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-30px) rotate(180deg);
    opacity: 0.4;
  }
  75% {
    transform: translateY(-10px) rotate(270deg);
    opacity: 0.7;
  }
}

/* Hero Section */
.support-hero {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2rem;
  background: linear-gradient(
    135deg,
    rgba(0, 120, 212, 0.1) 0%,
    rgba(99, 102, 241, 0.05) 100%
  );
  position: relative;
}

.support-hero::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
}

.support-badge {
  position: relative;
  display: inline-flex;
  align-items: center;
  padding: 6px 16px;
  background: rgba(0, 120, 212, 0.1);
  border: 1px solid rgba(0, 120, 212, 0.2);
  border-radius: 20px;
  margin-bottom: 1rem;
  backdrop-filter: blur(10px);
}

.support-title {
  font-size: 2.2rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
}

.title-highlight {
  background: linear-gradient(135deg, var(--primary-color), #4b96d7, #6366f1);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  position: relative;
}

.support-tagline {
  color: var(--text-secondary);
  font-size: 1.1rem;
  margin: 0;
  font-weight: 500;
}

/* Hero Visual */
.support-hero-visual {
  position: relative;
  flex-shrink: 0;
}

.hero-circle {
  position: relative;
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-color), #4b96d7);
  border-radius: 50%;
  box-shadow: 0 10px 30px rgba(0, 120, 212, 0.3);
}

.hero-icon {
  color: white;
  z-index: 10;
}

@keyframes icon-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.hero-ring {
  position: absolute;
  border: 2px solid;
  border-radius: 50%;
  border-color: transparent var(--primary-color) transparent transparent;
  animation: ring-rotate 4s linear infinite;
}

.ring-1 {
  width: 120px;
  height: 120px;
  animation-duration: 3s;
}

.ring-2 {
  width: 140px;
  height: 140px;
  animation-duration: 4s;
  animation-direction: reverse;
}

.ring-3 {
  width: 160px;
  height: 160px;
  animation-duration: 5s;
}

@keyframes ring-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Interactive Action Grid */
.support-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  padding: 2rem;
}

/* For larger screens, use 3-column layout with love card in center */
@media (min-width: 1024px) {
  .support-grid {
    grid-template-columns: repeat(3, 1fr);
    max-width: 1200px;
    margin: 0 auto;
  }
}

.support-action-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 1.5rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.support-action-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(
    90deg,
    transparent,
    var(--accent-color),
    transparent
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.support-action-card:hover {
  transform: translateY(-4px);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.support-action-card:hover::before {
  opacity: 1;
}

.bug-card {
  --accent-color: #ef4444;
}
.feature-card {
  --accent-color: #8b5cf6;
}
.love-card {
  --accent-color: #10b981;
}

.action-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.action-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
}

.bug-icon {
  background: linear-gradient(
    135deg,
    rgba(239, 68, 68, 0.15),
    rgba(239, 68, 68, 0.25)
  );
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.15);
}

.feature-icon {
  background: linear-gradient(
    135deg,
    rgba(139, 92, 246, 0.15),
    rgba(139, 92, 246, 0.25)
  );
  color: #8b5cf6;
  border: 1px solid rgba(139, 92, 246, 0.3);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.15);
}

.idea-icon {
  background: linear-gradient(
    135deg,
    rgba(245, 158, 11, 0.15),
    rgba(245, 158, 11, 0.25)
  );
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.3);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.15);
}

.love-icon {
  background: linear-gradient(
    135deg,
    rgba(16, 185, 129, 0.15),
    rgba(16, 185, 129, 0.25)
  );
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.3);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
}

.support-action-card:hover .action-icon {
  transform: scale(1.1) rotate(5deg);
}

.action-header h4 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
}

.action-description {
  color: var(--text-secondary);
  margin: 0 0 1.5rem 0;
  line-height: 1.5;
  font-size: 0.95rem;
}

/* Enhanced Action Buttons */
.action-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: var(--text-primary);
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.btn-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.btn-label {
  font-weight: 600;
  font-size: 1rem;
}

.btn-arrow {
  transition: all 0.3s ease;
  opacity: 0.7;
}

.btn-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.6s ease;
}

.action-btn:hover {
  transform: translateY(-2px);
  border-color: var(--accent-color);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.action-btn:hover .btn-glow {
  left: 100%;
}

.action-btn:hover .btn-arrow {
  transform: translateX(4px);
  opacity: 1;
}

/* Enhanced Default Button Styling */
.bug-btn {
  background: linear-gradient(
    135deg,
    rgba(239, 68, 68, 0.08),
    rgba(239, 68, 68, 0.12)
  );
  border-color: rgba(239, 68, 68, 0.25);
  color: #ef4444;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.1);
}

.bug-btn:hover {
  background: linear-gradient(
    135deg,
    rgba(239, 68, 68, 0.15),
    rgba(239, 68, 68, 0.2)
  );
  border-color: #ef4444;
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.25);
  color: #dc2626;
}

.feature-btn {
  background: linear-gradient(
    135deg,
    rgba(139, 92, 246, 0.08),
    rgba(139, 92, 246, 0.12)
  );
  border-color: rgba(139, 92, 246, 0.25);
  color: #8b5cf6;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.1);
}

.feature-btn:hover {
  background: linear-gradient(
    135deg,
    rgba(139, 92, 246, 0.15),
    rgba(139, 92, 246, 0.2)
  );
  border-color: #8b5cf6;
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.25);
  color: #7c3aed;
}

.love-btn {
  background: linear-gradient(
    135deg,
    rgba(16, 185, 129, 0.08),
    rgba(16, 185, 129, 0.12)
  );
  border-color: rgba(16, 185, 129, 0.25);
  color: #10b981;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.1);
}

.love-btn:hover {
  background: linear-gradient(
    135deg,
    rgba(16, 185, 129, 0.15),
    rgba(16, 185, 129, 0.2)
  );
  border-color: #10b981;
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.25);
  color: #059669;
}

.action-btn.primary {
  background: linear-gradient(135deg, #10b981, #059669) !important;
  color: white !important;
  border-color: #10b981 !important;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2) !important;
}

.action-btn.primary:hover {
  background: linear-gradient(135deg, #059669, #047857) !important;
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4) !important;
  color: white !important;
  border-color: #059669 !important;
}

/* Direct Connection Footer */
.support-footer {
  padding: 1.5rem 2rem 2rem;
  background: rgba(0, 0, 0, 0.02);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.direct-connect {
  max-width: 600px;
  margin: 0 auto;
}

.connect-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.connect-header h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.3rem;
  font-weight: 700;
  background: linear-gradient(135deg, var(--primary-color), #4b96d7);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.connect-header p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.95rem;
}

.connect-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.connect-btn {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.25rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.connect-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.6s ease;
}

.connect-btn:hover::before {
  left: 100%;
}

.connect-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

.connect-icon {
  width: 45px;
  height: 45px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.email-connect .connect-icon {
  background: linear-gradient(
    135deg,
    rgba(34, 197, 94, 0.1),
    rgba(34, 197, 94, 0.2)
  );
  color: #22c55e;
}

.linkedin-connect .connect-icon {
  background: linear-gradient(
    135deg,
    rgba(14, 118, 189, 0.1),
    rgba(14, 118, 189, 0.2)
  );
  color: #0e76bd;
}

.connect-btn:hover .connect-icon {
  transform: scale(1.1);
}

.connect-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.connect-label {
  font-weight: 600;
  font-size: 1rem;
  color: var(--text-primary);
}

.connect-detail {
  font-size: 0.85rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.email-connect:hover {
  background: rgba(34, 197, 94, 0.05);
}

.email-connect:hover .connect-label {
  color: #22c55e;
}

.linkedin-connect:hover {
  background: rgba(14, 118, 189, 0.05);
}

.linkedin-connect:hover .connect-label {
  color: #0e76bd;
}

.response-time {
  display: flex;
  justify-content: center;
  align-items: center;
}

.response-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.2);
  border-radius: 20px;
  font-size: 0.85rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.status-dot {
  width: 8px;
  height: 8px;
  background: #22c55e;
  border-radius: 50%;
  animation: status-pulse 2s ease-in-out infinite;
}

@keyframes status-pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.2);
  }
}

/* Responsive Design */
@media (min-width: 769px) and (max-width: 1023px) {
  .support-grid {
    grid-template-columns: repeat(2, 1fr);
    max-width: 800px;
    margin: 0 auto;
  }

  /* On tablet, show love card prominently */
  .love-card {
    grid-column: 1 / -1;
    max-width: 400px;
    margin: 0 auto;
  }
}

@media (max-width: 768px) {
  .support-hero {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
  }

  .support-grid {
    grid-template-columns: 1fr;
    padding: 1.5rem;
  }

  .connect-options {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .hero-circle {
    width: 80px;
    height: 80px;
  }

  .hero-icon {
    width: 32px;
    height: 32px;
  }
}

@media (max-width: 640px) {
  .support-hero,
  .support-grid,
  .support-footer {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .support-title {
    font-size: 1.8rem;
  }

  .support-action-card {
    padding: 1.25rem;
  }

  .connect-btn {
    padding: 0.875rem 1rem;
  }

  .connect-icon {
    width: 40px;
    height: 40px;
  }

  .response-indicator {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }
}

/* Enhanced Resources Section */
.resource-description {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  font-size: 1.05rem;
  line-height: 1.6;
}

.resource-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.resource-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: rgba(0, 120, 212, 0.02);
  border-radius: 12px;
  border: 1px solid rgba(0, 120, 212, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.resource-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, var(--primary-color), #4b96d7);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.resource-item:hover::before {
  transform: scaleY(1);
}

.resource-item:hover {
  background: rgba(0, 120, 212, 0.05);
  border-color: rgba(0, 120, 212, 0.2);
  transform: translateX(8px);
  box-shadow: 0 4px 12px rgba(0, 120, 212, 0.15);
}

.resource-icon {
  background: linear-gradient(
    135deg,
    rgba(0, 120, 212, 0.1),
    rgba(75, 150, 215, 0.15)
  );
  padding: 12px;
  border-radius: 12px;
  color: var(--primary-color);
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.resource-icon.visible {
  animation: bounce-in 0.6s ease;
}

@keyframes bounce-in {
  0% {
    transform: scale(0) rotate(180deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.2) rotate(10deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

.resource-details {
  flex: 1;
}

.resource-details h5 {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1.1rem;
}

.resource-details p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.95rem;
  line-height: 1.5;
}

/* Enhanced Loading States */
.developer-avatar.loading::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  animation: loading-shine 1.5s infinite;
  z-index: 1;
  border-radius: 50%;
}

@keyframes loading-shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.developer-avatar.error .avatar-placeholder {
  background: rgba(255, 99, 99, 0.1);
  border: 2px dashed rgba(255, 99, 99, 0.3);
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
  .about-card-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .developer-info {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-lg);
  }

  .developer-details h4::after {
    left: 50%;
    transform: translateX(-50%);
  }

  .developer-links {
    justify-content: center;
  }

  .support-buttons {
    flex-direction: column;
  }

  .btn-support {
    justify-content: center;
  }

  .about-header h2 {
    font-size: 1.8rem;
  }

  .feature-stats {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }
}

/* Enhanced Accessibility and Focus States */
.about-card:focus-within {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.btn-support:focus,
.dev-link:focus,
.testimonial-dot:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(0, 120, 212, 0.2);
}

/* Enhanced Animation Delays for Staggered Effects */
.about-card {
  animation: fadeInUp 0.6s ease forwards;
  opacity: 0;
  transform: translateY(30px);
}

.about-card:nth-child(1) {
  animation-delay: 0.1s;
}
.about-card:nth-child(2) {
  animation-delay: 0.2s;
}
.about-card:nth-child(3) {
  animation-delay: 0.3s;
}
.about-card:nth-child(4) {
  animation-delay: 0.4s;
}
.about-card:nth-child(5) {
  animation-delay: 0.5s;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Print Styles */
@media print {
  .about-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #ddd;
  }

  .support-wave,
  .avatar-ring,
  .testimonial-nav {
    display: none;
  }

  .about-card-row {
    grid-template-columns: 1fr;
  }

  .about-card-row .about-card {
    margin-bottom: var(--spacing-lg);
  }

  .developer-photo {
    border: 2px solid #333;
  }

  .btn-support,
  .dev-link {
    border: 1px solid #333;
    color: #333 !important;
    background: transparent !important;
  }

  body {
    font-size: 12pt;
    line-height: 1.4;
  }
}

/* New Enhanced Elements Styles */

/* Floating Sparkles Animation */
.floating-sparkles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
  z-index: 1;
}

.sparkle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, var(--primary-color), transparent);
  border-radius: 50%;
  animation: sparkle-float 4s ease-in-out infinite;
  opacity: 0.7;
}

.sparkle:nth-child(1) {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
  animation-duration: 3s;
}

.sparkle:nth-child(2) {
  top: 60%;
  right: 15%;
  animation-delay: 1s;
  animation-duration: 4s;
}

.sparkle:nth-child(3) {
  bottom: 30%;
  left: 70%;
  animation-delay: 2s;
  animation-duration: 3.5s;
}

@keyframes sparkle-float {
  0%,
  100% {
    transform: translateY(0px) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-20px) scale(1.2);
    opacity: 1;
  }
}

/* Enhanced Tooltips */
[data-tooltip] {
  position: relative;
}

[data-tooltip]:hover::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 120%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.8rem;
  white-space: nowrap;
  z-index: 1000;
  animation: tooltip-appear 0.3s ease;
}

[data-tooltip]:hover::after {
  content: "";
  position: absolute;
  bottom: 110%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.9);
  z-index: 1000;
  animation: tooltip-appear 0.3s ease;
}

@keyframes tooltip-appear {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* Enhanced Stat Indicators */
.stat-indicator {
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(
    90deg,
    transparent,
    var(--primary-color),
    transparent
  );
  border-radius: 2px;
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.stat-box:hover .stat-indicator {
  transform: scaleX(1);
  animation: indicator-glow 1s ease infinite;
}

@keyframes indicator-glow {
  0%,
  100% {
    box-shadow: 0 0 5px rgba(0, 120, 212, 0.5);
  }
  50% {
    box-shadow: 0 0 15px rgba(0, 120, 212, 0.8);
  }
}

/* Enhanced Testimonial Profiles */
.testimonial-profile {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm);
  background: rgba(0, 120, 212, 0.03);
  border-radius: 12px;
  border: 1px solid rgba(0, 120, 212, 0.1);
  transition: all 0.4s ease;
  transform: translateY(10px);
  opacity: 0;
}

.testimonial.active .testimonial-profile {
  animation: profileSlideIn 0.6s ease 0.2s forwards;
}

@keyframes profileSlideIn {
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.profile-avatar {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--primary-color), #4b96d7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 120, 212, 0.2);
  position: relative;
  overflow: hidden;
  transition: all 0.4s ease;
}

.testimonial.active .profile-avatar {
  animation: avatarBounce 0.6s ease 0.4s;
}

@keyframes avatarBounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0) scale(1);
  }
  40% {
    transform: translateY(-5px) scale(1.05);
  }
  60% {
    transform: translateY(-3px) scale(1.02);
  }
}

.profile-avatar::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: avatar-shine 3s ease-in-out infinite;
  opacity: 0;
}

.testimonial.active .profile-avatar::before {
  opacity: 1;
}

@keyframes avatar-shine {
  0%,
  100% {
    transform: rotate(0deg);
    opacity: 0;
  }
  50% {
    transform: rotate(180deg);
    opacity: 1;
  }
}

/* Enhanced Text Elements */
.testimonial-text {
  font-style: italic;
  color: var(--text-primary);
  text-align: center;
  margin-bottom: var(--spacing-md);
  font-size: 1.05rem;
  line-height: 1.6;
  position: relative;
  transform: translateY(20px);
  opacity: 0;
  transition: all 0.6s ease;
}

.testimonial.active .testimonial-text {
  animation: textReveal 0.8s ease 0.3s forwards;
}

@keyframes textReveal {
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  50% {
    transform: translateY(-5px);
    opacity: 0.7;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.testimonial-text::before,
.testimonial-text::after {
  content: '"';
  font-size: 2rem;
  color: var(--primary-color);
  opacity: 0.3;
  position: absolute;
  font-family: serif;
  transition: all 0.4s ease;
}

.testimonial.active .testimonial-text::before,
.testimonial.active .testimonial-text::after {
  animation: quoteGlow 1s ease 0.5s;
}

@keyframes quoteGlow {
  0%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
    text-shadow: 0 0 10px var(--primary-color);
  }
}

.testimonial-text::before {
  top: -10px;
  left: -15px;
}

.testimonial-text::after {
  bottom: -25px;
  right: -10px;
}

/* Enhanced Meta Information */
.testimonial-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-sm);
  border-top: 1px solid rgba(0, 120, 212, 0.1);
  font-size: 0.8rem;
  transform: translateY(15px);
  opacity: 0;
}

.testimonial.active .testimonial-meta {
  animation: metaSlideUp 0.6s ease 0.6s forwards;
}

@keyframes metaSlideUp {
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.verified-badge {
  background: linear-gradient(135deg, #10b981, #34d399);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 4px;
  transform: scale(0.8);
  transition: all 0.3s ease;
}

.testimonial.active .verified-badge {
  animation: badgePop 0.4s ease 0.8s forwards;
}

@keyframes badgePop {
  0% {
    transform: scale(0.8);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* Enhanced Navigation Dots */
.testimonial-nav {
  display: flex;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
  position: absolute;
  bottom: var(--spacing-md);
  left: 50%;
  transform: translateX(-50%);
}

.testimonial-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(0, 120, 212, 0.3);
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  border: 2px solid transparent;
  overflow: hidden;
}

.testimonial-dot::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, var(--primary-color), transparent);
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
  border-radius: 50%;
}

.testimonial-dot:hover {
  background: rgba(0, 120, 212, 0.6);
  transform: scale(1.3);
  box-shadow: 0 0 15px rgba(0, 120, 212, 0.4);
}

.testimonial-dot:hover::before {
  width: 20px;
  height: 20px;
  opacity: 0.3;
}

.testimonial-dot.active {
  background: var(--primary-color);
  transform: scale(1.4);
  box-shadow: 0 4px 12px rgba(0, 120, 212, 0.5);
}

.testimonial-dot.active::before {
  width: 25px;
  height: 25px;
  opacity: 0.2;
  animation: dotPulse 2s ease-in-out infinite;
}

@keyframes dotPulse {
  0%,
  100% {
    width: 25px;
    height: 25px;
    opacity: 0.2;
  }
  50% {
    width: 35px;
    height: 35px;
    opacity: 0.1;
  }
}

/* Enhanced Carousel Controls */
.carousel-controls {
  position: absolute;
  transform: translateY(55px);
  justify-content: space-around;
  left: 0;
  right: 0;
  display: flex;
  pointer-events: none;
  z-index: 20;
  padding: 0 var(--spacing-md);
}

.carousel-btn {
  background: rgba(0, 120, 212, 0.9);
  color: white;
  border: none;
  width: 45px;
  height: 45px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.3rem;
  font-weight: bold;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  pointer-events: auto;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(0, 120, 212, 0.3);
  opacity: 0;
  transform: scale(0.7) translateY(10px);
  position: relative;
  overflow: hidden;
}

.carousel-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.6s ease;
}

.testimonial-carousel:hover .carousel-btn {
  opacity: 1;
  transform: scale(1) translateY(0);
}

.carousel-btn:hover {
  background: var(--primary-color);
  transform: scale(1.15) translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 120, 212, 0.4);
}

.carousel-btn:hover::before {
  left: 100%;
}

.carousel-btn:active {
  transform: scale(1.05) translateY(0);
}

/* Enhanced Progress Bar */
.carousel-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: rgba(99, 102, 241, 0.2);
  overflow: hidden;
  border-radius: 0 0 12px 12px;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 50%, #06b6d4 100%);
  width: 0%;
  transition: width 0.1s linear;
  border-radius: inherit;
  position: relative;
  overflow: hidden;
}

.progress-bar::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: progressShimmer 2s ease-in-out infinite;
}

@keyframes progressShimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Testimonial Card Hover Effects */
.testimonial-card {
  transition: all 0.4s ease;
}

.testimonial-card:hover .testimonial-carousel {
  transform: scale(1.02);
}

.testimonial-card:hover .testimonial.active {
  transform: translateX(0) rotateY(0deg) scale(1.02);
}

/* Animation Variants for Different Directions */
.testimonial.slide-from-right {
  transform: translateX(100%) rotateY(45deg) scale(0.8);
}

.testimonial.slide-from-left {
  transform: translateX(-100%) rotateY(-45deg) scale(0.8);
}

.testimonial.slide-from-top {
  transform: translateY(-50px) rotateX(45deg) scale(0.8);
}

.testimonial.slide-from-bottom {
  transform: translateY(50px) rotateX(-45deg) scale(0.8);
}

/* Performance Optimizations */
.testimonial-carousel,
.testimonial,
.carousel-btn,
.testimonial-dot {
  will-change: transform, opacity;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .testimonial,
  .testimonial-profile,
  .testimonial-text,
  .testimonial-meta,
  .verified-badge,
  .carousel-btn,
  .testimonial-dot {
    animation: none !important;
    transition: opacity 0.3s ease !important;
  }

  .testimonial {
    transform: none !important;
  }

  .testimonial:not(.active) {
    opacity: 0;
  }

  .testimonial.active {
    opacity: 1;
  }
}

/* Enhanced Developer Profile Styles */

/* Developer Showcase Container */
.developer-showcase {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
  position: relative;
}

/* Enhanced Developer Header */
.developer-header {
  display: flex;
  gap: var(--spacing-xl);
  align-items: flex-start;
  margin-bottom: var(--spacing-lg);
}

/* Enhanced Avatar Section */
.developer-avatar-enhanced {
  position: relative;
  width: 140px;
  height: 140px;
  flex-shrink: 0;
}

.avatar-status {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 3px solid white;
  z-index: 5;
  animation: pulse-status 2s ease-in-out infinite;
}

.avatar-status.online {
  background: #10b981;
  box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
}

@keyframes pulse-status {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

.avatar-ring-enhanced {
  position: absolute;
  top: -8px;
  left: -8px;
  right: -8px;
  bottom: -8px;
  border-radius: 50%;
  background: conic-gradient(
    from 0deg,
    var(--primary-color),
    #4b96d7,
    #6366f1,
    var(--primary-color)
  );
  animation: rotate-ring 8s linear infinite;
  opacity: 0.8;
}

@keyframes rotate-ring {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.avatar-glow {
  position: absolute;
  top: -15px;
  left: -15px;
  right: -15px;
  bottom: -15px;
  border-radius: 50%;
  background: radial-gradient(
    circle,
    rgba(0, 120, 212, 0.2) 0%,
    transparent 70%
  );
  animation: glow-pulse 3s ease-in-out infinite alternate;
  z-index: 0;
}

@keyframes glow-pulse {
  from {
    opacity: 0.3;
    transform: scale(0.95);
  }
  to {
    opacity: 0.6;
    transform: scale(1.05);
  }
}

.developer-photo-enhanced {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid white;
  position: relative;
  z-index: 3;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.developer-avatar-enhanced:hover .developer-photo-enhanced {
  transform: scale(1.05);
  box-shadow: 0 10px 30px rgba(0, 120, 212, 0.4);
}

.avatar-badge {
  position: absolute;
  bottom: 5px;
  right: 5px;
  width: 35px;
  height: 35px;
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid white;
  z-index: 5;
  animation: badge-bounce 2s ease-in-out infinite;
}

@keyframes badge-bounce {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-3px) rotate(5deg);
  }
}

.badge-icon {
  font-size: 1.2rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

/* Developer Introduction */
.developer-intro {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.intro-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.section-title {
  margin: 0;
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--text-primary);
  background: linear-gradient(135deg, var(--primary-color), #4b96d7);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.name-section {
  position: relative;
}

.developer-name {
  margin: 0;
  font-size: 2.2rem;
  font-weight: 700;
  color: var(--text-primary);
  letter-spacing: -0.5px;
  position: relative;
}

.name-decoration {
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), #4b96d7);
  border-radius: 2px;
  margin-top: 8px;
  animation: decoration-expand 2s ease-in-out infinite;
}

@keyframes decoration-expand {
  0%,
  100% {
    width: 80px;
  }
  50% {
    width: 100px;
  }
}

/* Professional Quote */
.developer-quote {
  background: linear-gradient(
    135deg,
    rgba(0, 120, 212, 0.05),
    rgba(75, 150, 215, 0.08)
  );
  border-left: 4px solid var(--primary-color);
  padding: var(--spacing-lg);
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.developer-quote::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y="50" font-size="100" opacity="0.03">💡</text></svg>')
    no-repeat;
  background-position: right 20px top 20px;
  background-size: 60px;
  pointer-events: none;
}

.quote-mark {
  font-size: 3rem;
  color: var(--primary-color);
  opacity: 0.3;
  line-height: 1;
  margin-bottom: -10px;
  font-family: serif;
}

.quote-text {
  font-style: italic;
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
  font-weight: 500;
}

.quote-author {
  font-size: 0.9rem;
  color: var(--text-secondary);
  text-align: right;
  font-weight: 600;
}

/* Enhanced Bio */
.developer-bio-enhanced {
  background: rgba(0, 120, 212, 0.03);
  padding: var(--spacing-lg);
  border-radius: 12px;
  border: 1px solid rgba(0, 120, 212, 0.1);
}

.bio-text {
  margin: 0;
  line-height: 1.7;
  color: var(--text-secondary);
  font-size: 1rem;
}

/* Enhanced Social Links */
.developer-social {
  background: var(--bg-primary);
  padding: var(--spacing-lg);
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.social-title {
  margin: 0 0 var(--spacing-lg) 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.social-links-enhanced {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-md);
}

.social-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: white;
  border-radius: 12px;
  border: 1px solid var(--border-color);
  text-decoration: none;
  color: var(--text-primary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.link-background {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  transition: left 0.4s ease;
  z-index: 1;
}

.social-link.github .link-background {
  background: linear-gradient(135deg, #333, #555);
}

.social-link.linkedin .link-background {
  background: linear-gradient(135deg, #0077b5, #00a0dc);
}

.social-link.email .link-background {
  background: linear-gradient(135deg, #dc2626, #ef4444);
}

.social-link.twitter .link-background {
  background: linear-gradient(135deg, #1da1f2, #0891b2);
}

.social-link:hover .link-background {
  left: 0;
}

.social-link:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  color: white;
}

.link-icon {
  width: 45px;
  height: 45px;
  background: rgba(0, 120, 212, 0.1);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.social-link:hover .link-icon {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1) rotate(5deg);
}

.link-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
  position: relative;
  z-index: 2;
}

.link-title {
  font-weight: 600;
  font-size: 1rem;
}

.link-subtitle {
  font-size: 0.8rem;
  opacity: 0.8;
}

.link-arrow {
  font-size: 1.2rem;
  position: relative;
  z-index: 2;
  transition: transform 0.3s ease;
}

.social-link:hover .link-arrow {
  transform: translateX(4px);
}

/* Enhanced Developer CTA */
.developer-cta {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
  background: linear-gradient(
    145deg,
    var(--bg-primary) 0%,
    var(--bg-secondary) 100%
  );
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-xl);
  border: 1px solid var(--border-color);
  transition: all var(--transition-medium);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.developer-cta::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--accent-color) 100%
  );
  opacity: 0;
  transition: opacity var(--transition-medium);
  z-index: 1;
}

.developer-cta:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-light);
}

.developer-cta:hover::before {
  opacity: 0.05;
}

.cta-icon-wrapper {
  position: relative;
  flex-shrink: 0;
  z-index: 2;
}

.developer-avatar-cta {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--primary-color);
  box-shadow: var(--shadow-glow);
  transition: all var(--transition-bounce);
}

.developer-cta:hover .developer-avatar-cta {
  transform: scale(1.1) rotate(5deg);
  border-color: var(--accent-color);
}

.cta-text-content {
  flex-grow: 1;
  z-index: 2;
}

.cta-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.cta-description {
  font-size: var(--font-size-md);
  color: var(--text-secondary);
  margin: 0;
  line-height: var(--line-height-relaxed);
  max-width: 380px;
}

.cta-button-new {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  background: var(--gradient-primary);
  color: var(--text-inverse);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-md);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  text-decoration: none;
  transition: all var(--transition-bounce);
  position: relative;
  z-index: 2;
  box-shadow: var(--shadow-md);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.cta-button-new:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

.cta-button-new .arrow-icon {
  width: 20px;
  height: 20px;
  transition: transform var(--transition-medium);
}

.cta-button-new:hover .arrow-icon {
  transform: translateX(4px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .developer-cta {
    flex-direction: column;
    text-align: center;
    padding: var(--spacing-lg);
  }

  .cta-text-content {
    margin: var(--spacing-md) 0;
  }

  .cta-description {
    margin: 0 auto;
  }

  .developer-avatar-cta {
    width: 100px;
    height: 100px;
  }
}

@media (max-width: 480px) {
  .developer-cta {
    padding: var(--spacing-md);
  }

  .cta-title {
    font-size: var(--font-size-lg);
  }

  .cta-description {
    font-size: var(--font-size-sm);
  }

  .cta-button-new {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
  }
}

/* Modern Feature Highlight Card Enhancement */
.feature-highlight-card-modern {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.98) 100%
  );
  border: 1px solid rgba(226, 232, 240, 0.5);
  border-radius: 20px;
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06), 0 1px 2px rgba(0, 0, 0, 0.04);
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.feature-highlight-card-modern:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.08),
    0 8px 16px rgba(99, 102, 241, 0.08);
  border-color: rgba(99, 102, 241, 0.2);
}

.card-background-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 20% 20%,
      rgba(99, 102, 241, 0.03) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 80%,
      rgba(6, 182, 212, 0.03) 0%,
      transparent 50%
    );
  pointer-events: none;
}

.card-content-modern {
  padding: 2rem;
  position: relative;
  z-index: 2;
}

/* Modern Hero Section */
.feature-hero {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.hero-icon {
  position: relative;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--accent-color)
  );
  border-radius: 12px;
  color: white;
}

.icon-glow-ring {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--accent-color)
  );
  border-radius: 16px;
  opacity: 0.2;
  filter: blur(8px);
  animation: glow-pulse 3s ease-in-out infinite;
}

@keyframes glow-pulse {
  0%,
  100% {
    opacity: 0.2;
    transform: scale(0.95);
  }
  50% {
    opacity: 0.4;
    transform: scale(1.05);
  }
}

.hero-content {
  flex: 1;
}

.hero-title {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  letter-spacing: -0.02em;
}

.hero-badges {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-primary {
  background: linear-gradient(
    135deg,
    rgba(99, 102, 241, 0.1),
    rgba(139, 92, 246, 0.1)
  );
  color: var(--primary-color);
  border: 1px solid rgba(99, 102, 241, 0.2);
}

.badge-success {
  background: linear-gradient(
    135deg,
    rgba(16, 185, 129, 0.1),
    rgba(6, 182, 212, 0.1)
  );
  color: var(--success-color);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

/* Enhanced Description */
.feature-description-modern {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 2rem;
  font-size: 1rem;
}

/* Modern Feature Grid */
.feature-grid-modern {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1rem;
  margin-bottom: 0;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 1rem;
  background: rgba(248, 250, 252, 0.5);
  border: 1px solid rgba(226, 232, 240, 0.5);
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.feature-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(99, 102, 241, 0.05),
    rgba(6, 182, 212, 0.05)
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.feature-item:hover::before {
  opacity: 1;
}

.feature-item:hover {
  transform: translateY(-2px);
  border-color: rgba(99, 102, 241, 0.3);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
}

.feature-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    135deg,
    rgba(99, 102, 241, 0.1),
    rgba(6, 182, 212, 0.1)
  );
  border-radius: 10px;
  color: var(--primary-color);
  margin-bottom: 0.75rem;
  position: relative;
  z-index: 2;
}

.feature-content {
  position: relative;
  z-index: 2;
}

.feature-number {
  display: block;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.feature-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .card-content-modern {
    padding: 1.5rem;
  }

  .feature-hero {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .feature-grid-modern {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }
}

@media (max-width: 480px) {
  .feature-grid-modern {
    grid-template-columns: 1fr;
  }

  .hero-badges {
    justify-content: center;
  }
}

/* Enhanced Resources/Powered By Card Styles */
.resources-card {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.98) 50%,
    rgba(241, 245, 249, 0.95) 100%
  );
  border: 1px solid rgba(99, 102, 241, 0.1);
  backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
}

.resources-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(
    90deg,
    #6366f1 0%,
    #8b5cf6 25%,
    #06b6d4 50%,
    #10b981 75%,
    #f59e0b 100%
  );
  animation: gradient-flow 3s ease-in-out infinite;
}

.resources-card .card-content {
  position: relative;
  z-index: 2;
}

.resources-card h3 {
  background: linear-gradient(135deg, #1e293b, #475569, #6366f1);
  background-size: 200% auto;
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: var(--spacing-lg);
  animation: gradient-shift 4s ease infinite;
  position: relative;
}

.resources-card h3::after {
  content: "🚀";
  position: absolute;
  right: -30px;
  top: 0;
  font-size: 1.2rem;
  animation: rocket-bounce 2s ease-in-out infinite;
}

@keyframes rocket-bounce {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-3px) rotate(15deg);
  }
}

.resource-description {
  color: var(--text-secondary);
  font-size: 0.95rem;
  line-height: 1.6;
  margin-bottom: var(--spacing-xl);
  position: relative;
  padding-left: 20px;
}

.resource-description::before {
  content: "💡";
  position: absolute;
  left: 0;
  top: 0;
  font-size: 1rem;
  animation: pulse-glow 2s ease-in-out infinite;
}

.resource-list {
  display: grid;
  gap: var(--spacing-lg);
}

.resource-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(248, 250, 252, 0.9) 100%
  );
  border: 1px solid rgba(226, 232, 240, 0.6);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.resource-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(99, 102, 241, 0.1),
    transparent
  );
  transition: left 0.6s ease;
}

.resource-item:hover::before {
  left: 100%;
}

.resource-item:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 12px 24px rgba(99, 102, 241, 0.15), 0 4px 8px rgba(0, 0, 0, 0.1);
  border-color: rgba(99, 102, 241, 0.3);
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.98) 100%
  );
}

.resource-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  flex-shrink: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.resource-item:nth-child(1) .resource-icon {
  background: linear-gradient(135deg, #fef3c7, #f59e0b);
  color: #92400e;
}

.resource-item:nth-child(2) .resource-icon {
  background: linear-gradient(135deg, #dbeafe, #3b82f6);
  color: #1e40af;
}

.resource-item:nth-child(3) .resource-icon {
  background: linear-gradient(135deg, #d1fae5, #10b981);
  color: #065f46;
}

.resource-item:nth-child(4) .resource-icon {
  background: linear-gradient(135deg, #fce7f3, #ec4899);
  color: #be185d;
}

.resource-item:nth-child(5) .resource-icon {
  background: linear-gradient(135deg, #e0e7ff, #6366f1);
  color: #3730a3;
}

.resource-icon::after {
  content: "";
  position: absolute;
  inset: -2px;
  border-radius: inherit;
  padding: 2px;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.resource-item:hover .resource-icon::after {
  opacity: 1;
}

.resource-item:hover .resource-icon {
  transform: rotate(5deg) scale(1.1);
}

.resource-icon svg {
  width: 24px;
  height: 24px;
  transition: all 0.3s ease;
}

.resource-item:hover .resource-icon svg {
  transform: scale(1.1);
}

.resource-details {
  flex: 1;
  min-width: 0;
}

.resource-details h5 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  transition: color 0.3s ease;
  position: relative;
}

.resource-item:hover .resource-details h5 {
  color: var(--primary-color);
}

.resource-details h5::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  transition: width 0.4s ease;
}

.resource-item:hover .resource-details h5::after {
  width: 100%;
}

.resource-details p {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
  line-height: 1.5;
  transition: color 0.3s ease;
}

.resource-item:hover .resource-details p {
  color: var(--text-primary);
}

/* Enhanced animations */
@keyframes pulse-glow {
  0%,
  100% {
    filter: brightness(1) drop-shadow(0 0 0 transparent);
  }
  50% {
    filter: brightness(1.2) drop-shadow(0 0 8px rgba(59, 130, 246, 0.4));
  }
}

/* Dark mode support */
[data-theme="dark"] .resources-card {
  background: linear-gradient(
    135deg,
    rgba(15, 23, 42, 0.95) 0%,
    rgba(30, 41, 59, 0.98) 50%,
    rgba(51, 65, 85, 0.95) 100%
  );
  border-color: rgba(99, 102, 241, 0.2);
}

[data-theme="dark"] .resource-item {
  background: linear-gradient(
    135deg,
    rgba(30, 41, 59, 0.8) 0%,
    rgba(51, 65, 85, 0.9) 100%
  );
  border-color: rgba(100, 116, 139, 0.3);
}

[data-theme="dark"] .resource-item:hover {
  background: linear-gradient(
    135deg,
    rgba(30, 41, 59, 0.95) 0%,
    rgba(51, 65, 85, 0.98) 100%
  );
  box-shadow: 0 12px 24px rgba(99, 102, 241, 0.2), 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .resource-item {
    padding: var(--spacing-md);
    gap: var(--spacing-sm);
  }

  .resource-icon {
    width: 40px;
    height: 40px;
  }

  .resource-icon svg {
    width: 20px;
    height: 20px;
  }

  .resource-details h5 {
    font-size: 1rem;
  }

  .resource-details p {
    font-size: 0.85rem;
  }
}

/* Accessibility improvements */
.resource-item:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
  .resource-item,
  .resource-icon,
  .resource-icon svg,
  .resource-details h5::after {
    transition: none;
  }

  .resources-card h3,
  .resource-item::before {
    animation: none;
  }
}

/* Community Stats Section */
.community-stats {
  position: relative;
}

.community-stats::before {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    var(--primary-color),
    transparent
  );
}

.stat-item {
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-md);
  transition: all 0.3s ease;
  cursor: default;
}

.stat-item:hover {
  background: rgba(99, 102, 241, 0.05);
  transform: translateY(-2px);
}

.stat-item:hover div:first-child {
  transform: scale(1.1);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced Tooltip Styles */
[data-tooltip] {
  position: relative;
}

[data-tooltip]:hover::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(15, 23, 42, 0.95);
  color: white;
  padding: 8px 12px;
  border-radius: var(--border-radius-md);
  font-size: 0.8rem;
  white-space: nowrap;
  opacity: 0;
  animation: tooltip-appear 0.3s ease forwards;
  z-index: 1000;
  pointer-events: none;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  margin-bottom: 8px;
}

[data-tooltip]:hover::after {
  content: "";
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: rgba(15, 23, 42, 0.95);
  opacity: 0;
  animation: tooltip-appear 0.3s ease forwards;
  z-index: 1000;
  margin-bottom: 4px;
}

@keyframes tooltip-appear {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(4px);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* Resource Item Enhanced Interactions */
.resource-item:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.resource-item:active {
  transform: translateY(-2px) scale(1.01);
  transition: transform 0.1s ease;
}

/* Loading state for resource items */
.resource-item.loading {
  opacity: 0.7;
  pointer-events: none;
}

.resource-item.loading .resource-icon {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Enhanced focus indicators for accessibility */
.resource-item:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
}

.stat-item:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Dark mode tooltips */
[data-theme="dark"] [data-tooltip]:hover::before {
  background: rgba(248, 250, 252, 0.95);
  color: #0f172a;
}

[data-theme="dark"] [data-tooltip]:hover::after {
  border-top-color: rgba(248, 250, 252, 0.95);
}

/* Mobile tooltip adjustments */
@media (max-width: 768px) {
  [data-tooltip]:hover::before {
    font-size: 0.75rem;
    padding: 6px 10px;
    max-width: 200px;
    white-space: normal;
    text-align: center;
  }
}

/* Print styles */
@media print {
  .resource-item:hover::before,
  .resource-item::before,
  .resources-card::before {
    display: none;
  }

  .community-stats {
    border-top: 1px solid #ccc !important;
  }
}
